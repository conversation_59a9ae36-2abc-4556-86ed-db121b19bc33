@import '../../../styles/variables.css';

/* 按鈕基礎樣式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  user-select: none;
}

/* 按鈕變體 */
.primary {
  background-color: var(--primary-color);
  color: white;
}

.primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.secondary {
  background-color: var(--secondary-color);
  color: white;
}

.secondary:hover:not(:disabled) {
  background-color: var(--secondary-dark);
}

.outline {
  background-color: transparent;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.outline:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: white;
}

.text {
  background-color: transparent;
  border-color: transparent;
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
}

.text:hover:not(:disabled) {
  background-color: rgba(46, 125, 50, 0.1);
}

.danger {
  background-color: #d32f2f;
  color: white;
}

.danger:hover:not(:disabled) {
  background-color: #b71c1c;
}

/* 按鈕尺寸 */
.sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
}

.md {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
}

.lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-md);
}

/* 按鈕狀態 */
.fullWidth {
  width: 100%;
}

.disabled,
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading {
  position: relative;
  cursor: wait;
}

.loading > * {
  visibility: hidden;
}

.spinner {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid;
  border-radius: 50%;
  border-color: currentColor currentColor currentColor transparent;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 圖標按鈕 */
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.iconOnly {
  padding: var(--spacing-sm);
}

.iconOnly.sm {
  padding: var(--spacing-xs);
}

.iconOnly.lg {
  padding: var(--spacing-md);
}

/* 按鈕組 */
.group {
  display: inline-flex;
  gap: var(--spacing-xs);
}

.group.vertical {
  flex-direction: column;
}

/* 按鈕組中的按鈕 */
.group .btn {
  margin: 0;
}

.group.vertical .btn {
  width: 100%;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .lg {
    padding: 0.5rem 1rem;
    font-size: var(--font-size-md);
  }
}

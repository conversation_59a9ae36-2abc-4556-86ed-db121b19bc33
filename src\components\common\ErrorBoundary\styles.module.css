.errorContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--spacing-lg);
  background-color: var(--background);
}

.errorContent {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--paper);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  max-width: 500px;
  width: 100%;
}

.errorTitle {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md);
}

.errorMessage {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-lg);
  line-height: 1.5;
}

.retryButton {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius-sm);
  background-color: var(--primary-color);
  color: white;
  font-size: var(--font-size-base);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retryButton:hover {
  background-color: var(--primary-dark);
}

.retryButton:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-light);
} 
import React from 'react';
import { Button } from './index';
import { FiPlus, FiArrowRight } from 'react-icons/fi';

export default {
  title: 'Components/Button',
  component: Button,
};

export const Primary = () => <Button variant="contained">Primary Button</Button>;

export const Outline = () => <Button variant="outlined">Outline Button</Button>;

export const Text = () => <Button variant="text">Text Button</Button>;

export const Sizes = () => (
  <div style={{ display: 'flex', gap: '8px' }}>
    <Button size="small">Small</Button>
    <Button size="medium">Medium</Button>
    <Button size="large">Large</Button>
  </div>
);

export const WithIcons = () => (
  <div style={{ display: 'flex', gap: '1rem', flexDirection: 'column' }}>
    <Button startIcon={<FiPlus />}>Add Item</Button>
    <Button endIcon={<FiArrowRight />}>Next Step</Button>
    <Button startIcon={<FiPlus />} endIcon={<FiArrowRight />}>
      Both Icons
    </Button>
  </div>
);

export const Loading = () => (
  <div style={{ display: 'flex', gap: '8px' }}>
    <Button loading variant="contained">
      Loading Primary
    </Button>
    <Button loading variant="outlined">
      Loading Outline
    </Button>
    <Button loading variant="text">
      Loading Text
    </Button>
  </div>
);

export const FullWidth = () => (
  <div style={{ width: '300px' }}>
    <Button fullWidth>Full Width Button</Button>
  </div>
);

export const Disabled = () => (
  <div style={{ display: 'flex', gap: '8px' }}>
    <Button disabled variant="contained">
      Disabled Primary
    </Button>
    <Button disabled variant="outlined">
      Disabled Outline
    </Button>
    <Button disabled variant="text">
      Disabled Text
    </Button>
  </div>
);

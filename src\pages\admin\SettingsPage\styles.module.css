/* 設定頁面樣式 */
.settingsContainer {
  width: 100%;
  display: grid;
  grid-template-columns: 240px 1fr;
  gap: var(--admin-space-xl);
}

/* 側邊選單 */
.settingsNav {
  position: sticky;
  top: var(--admin-space-lg);
  height: fit-content;
}

.navList {
  list-style: none;
  padding: 0;
  margin: 0;
  background-color: var(--admin-bg-white);
  border-radius: var(--admin-radius-lg);
  box-shadow: var(--admin-shadow-sm);
  overflow: hidden;
}

.navItem {
  border-bottom: 1px solid var(--admin-border-color);
}

.navItem:last-child {
  border-bottom: none;
}

.navLink {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  padding: var(--admin-space-md);
  color: var(--admin-text-medium);
  font-size: var(--admin-font-base);
  text-decoration: none;
  transition: all var(--admin-transition-normal);
}

.navLink:hover {
  background-color: var(--admin-bg-light);
  color: var(--admin-text-dark);
}

.navLinkActive {
  background-color: var(--admin-primary-light);
  color: var(--admin-primary);
  font-weight: var(--admin-font-medium);
}

.navIcon {
  width: 20px;
  height: 20px;
}

/* 設定內容區域 */
.settingsContent {
  background-color: var(--admin-bg-white);
  border-radius: var(--admin-radius-lg);
  box-shadow: var(--admin-shadow-sm);
  padding: var(--admin-space-xl);
}

.sectionTitle {
  font-size: var(--admin-font-xl);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-dark);
  margin-bottom: var(--admin-space-lg);
}

/* 表單樣式 */
.formGroup {
  margin-bottom: var(--admin-space-lg);
}

.formLabel {
  display: block;
  font-size: var(--admin-font-base);
  font-weight: var(--admin-font-medium);
  color: var(--admin-text-dark);
  margin-bottom: var(--admin-space-xs);
}

.formDescription {
  font-size: var(--admin-font-sm);
  color: var(--admin-text-light);
  margin-bottom: var(--admin-space-sm);
}

.formInput {
  width: 100%;
  padding: var(--admin-space-sm) var(--admin-space-md);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-font-base);
  color: var(--admin-text-dark);
  transition: all var(--admin-transition-normal);
}

.formInput:focus {
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px var(--admin-primary-light);
  outline: none;
}

.formInput:disabled {
  background-color: var(--admin-bg-light);
  cursor: not-allowed;
}

/* 開關按鈕 */
.toggleSwitch {
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
}

.toggleLabel {
  font-size: var(--admin-font-base);
  color: var(--admin-text-dark);
}

/* 分隔線 */
.divider {
  height: 1px;
  background-color: var(--admin-border-color);
  margin: var(--admin-space-xl) 0;
}

/* 操作按鈕區域 */
.actionButtons {
  display: flex;
  justify-content: flex-end;
  gap: var(--admin-space-md);
  margin-top: var(--admin-space-xl);
  padding-top: var(--admin-space-lg);
  border-top: 1px solid var(--admin-border-color);
}

/* 響應式設計 */
@media (max-width: 992px) {
  .settingsContainer {
    grid-template-columns: 200px 1fr;
    gap: var(--admin-space-lg);
  }
}

@media (max-width: 768px) {
  .settingsContainer {
    grid-template-columns: 1fr;
  }

  .settingsNav {
    position: static;
    margin-bottom: var(--admin-space-lg);
  }

  .navList {
    display: flex;
    flex-wrap: wrap;
    gap: var(--admin-space-xs);
    background: none;
    box-shadow: none;
  }

  .navItem {
    flex: 1;
    min-width: 150px;
    border: 1px solid var(--admin-border-color);
    border-radius: var(--admin-radius-md);
  }

  .settingsContent {
    padding: var(--admin-space-lg);
  }

  .actionButtons {
    flex-direction: column;
  }

  .actionButtons button {
    width: 100%;
  }
} 
/* 活動管理頁面樣式 */
.eventsContainer {
  width: 100%;
}

/* 活動卡片列表 */
.eventGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--admin-space-lg);
  margin-top: var(--admin-space-lg);
}

/* 活動卡片 */
.eventCard {
  background-color: var(--admin-bg-white);
  border-radius: var(--admin-radius-lg);
  box-shadow: var(--admin-shadow-sm);
  overflow: hidden;
  transition: all var(--admin-transition-normal);
}

.eventCard:hover {
  box-shadow: var(--admin-shadow-md);
  transform: translateY(-2px);
}

/* 活動圖片容器 */
.imageContainer {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 比例 */
  background-color: var(--admin-bg-light);
}

.eventImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 活動內容 */
.eventContent {
  padding: var(--admin-space-md);
}

.eventTitle {
  font-size: var(--admin-font-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-dark);
  margin-bottom: var(--admin-space-xs);
  line-height: 1.4;
}

.eventDescription {
  font-size: var(--admin-font-base);
  color: var(--admin-text-medium);
  margin-bottom: var(--admin-space-md);
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 活動資訊列表 */
.eventInfo {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-sm);
  margin-bottom: var(--admin-space-md);
}

.infoItem {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  color: var(--admin-text-medium);
  font-size: var(--admin-font-sm);
}

.infoIcon {
  width: 16px;
  height: 16px;
  color: var(--admin-text-light);
}

/* 活動狀態標籤 */
.statusBadge {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-font-sm);
  font-weight: var(--admin-font-medium);
  margin-bottom: var(--admin-space-md);
}

.statusactive {
  background-color: var(--admin-status-published-bg);
  color: var(--admin-status-published-text);
}

.statusdraft {
  background-color: var(--admin-status-draft-bg);
  color: var(--admin-status-draft-text);
}

.statusarchived {
  background-color: var(--admin-status-archived-bg);
  color: var(--admin-status-archived-text);
}

/* 活動操作按鈕 */
.cardActions {
  display: flex;
  gap: var(--admin-space-sm);
  padding-top: var(--admin-space-md);
  border-top: 1px solid var(--admin-border-color);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .eventGrid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--admin-space-md);
  }

  .eventContent {
    padding: var(--admin-space-sm);
  }

  .cardActions {
    flex-direction: column;
  }

  .cardActions button {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .eventGrid {
    grid-template-columns: 1fr;
  }
} 
import React, { useState, useEffect, useCallback } from 'react';
import Header from '../../../components/user/Header';
import ProjectDetails from '../../../components/user/ProjectDetails';
import ItemCard from '../../../components/user/ItemCard';
import OrderSummary from '../../../components/user/OrderSummary';
import BottomNav from '../../../components/user/BottomNav';
import {
  fetchActiveProjectWithItems,
  fetchProjectDiscounts,
  createOrderWithItems,
  supabase,
} from '../../../lib/supabase';
import type { Project, Item, Discount } from '../../../lib/supabase';
import { useCart } from '../../../contexts/CartContext';
import '../../../styles/nav-icons.css';
import styles from './styles.module.css'; // Import CSS Module

interface EstimatedDiscount {
  discountId: string | null;
  discountAmount: number;
  discountName?: string;
  discountDescription?: string;
}

const PreorderPage: React.FC = () => {
  const { items: cartItems, updateItemQuantity, clearCart, totalCount } = useCart();
  const [project, setProject] = useState<Project | null>(null);
  const [items, setItems] = useState<(Item & { quantity: number })[]>([]);
  const [discounts, setDiscounts] = useState<Discount[]>([]);
  const [estimatedDiscount, setEstimatedDiscount] = useState<EstimatedDiscount | null>(null);
  const [isLoadingDiscount, setIsLoadingDiscount] = useState(false);

  const [remarks, setRemarks] = useState('');
  const [pickupDate, setPickupDate] = useState('');
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');

  useEffect(() => {
    const loadData = async () => {
      try {
        const { project, items } = await fetchActiveProjectWithItems();
        setProject(project);
        setItems(
          items.map(item => ({
            ...item,
            quantity: cartItems.find(ci => ci.id === item.id)?.quantity ?? 0,
          }))
        );
        if (project?.id) {
          const fetchedDiscounts = await fetchProjectDiscounts(project.id);
          setDiscounts(fetchedDiscounts);
        }
      } catch (error) {
        console.error('載入專案失敗', error);
      }
    };
    loadData();
    // eslint-disable-next-line
  }, []);

  // 折扣預估
  const estimateDiscount = useCallback(async () => {
    if (!project?.id || cartItems.length === 0) {
      setEstimatedDiscount(null);
      return;
    }
    setIsLoadingDiscount(true);
    try {
      const rpcCartItems = cartItems.map(item => ({
        item_id: item.id,
        quantity: item.quantity,
        unit_price: item.price ?? 0,
      }));
      const { data, error } = await supabase.rpc('calculate_order_discount', {
        p_project_id: project.id,
        p_cart_items: rpcCartItems,
      });
      if (error) throw error;
      if (data && data.length > 0) {
        const result = data[0];
        const discountDetails = discounts.find(d => d.id === result.discount_id);
        setEstimatedDiscount({
          discountId: result.discount_id,
          discountAmount: result.discount_amount,
          discountName: discountDetails?.name,
          // Provide undefined if description is null
          discountDescription: discountDetails?.description ?? undefined,
        });
      } else {
        setEstimatedDiscount(null);
      }
    } catch (error) {
      setEstimatedDiscount(null);
      console.error('預估折扣計算失敗:', error);
    } finally {
      setIsLoadingDiscount(false);
    }
  }, [cartItems, project?.id, discounts]);

  useEffect(() => {
    const debounce = setTimeout(() => {
      estimateDiscount();
    }, 300);
    return () => clearTimeout(debounce);
  }, [estimateDiscount]);

  const handleQuantityChange = (id: string, quantity: number) => {
    updateItemQuantity(id, quantity);
  };

  const subtotal = cartItems.reduce((sum, item) => sum + item.subtotal, 0);
  const discount = estimatedDiscount?.discountAmount ?? 0;
  const total = Math.max(0, subtotal - discount);

  const appliedDiscount = estimatedDiscount?.discountId
    ? {
        id: estimatedDiscount.discountId,
        name: estimatedDiscount.discountName ?? '適用折扣',
        description: estimatedDiscount.discountDescription ?? '',
      }
    : null;

  const handleSubmit = async () => {
    if (!project || cartItems.length === 0) return;
    try {
      const userId = 'demo-user-id'; // TODO: 改為實際登入用戶ID
      await createOrderWithItems({
        userId,
        projectId: project.id,
        remarks,
        pickupDate: pickupDate || '',
        cartItems: cartItems.map(item => ({
          item_id: item.id,
          item_name: item.name ?? '',
          unit_price: item.price ?? 0,
          quantity: item.quantity,
          subtotal: item.subtotal,
        })),
      });
      setMessage('訂單送出成功！');
      setMessageType('success');
      clearCart();
      setRemarks('');
      setPickupDate('');
    } catch (error) {
      setMessage('訂單送出失敗，請稍後再試');
      setMessageType('error');
      console.error('訂單送出失敗', error);
    }
  };

  const handleRemoveItem = (id: string) => {
    updateItemQuantity(id, 0);
  };

  return (
    <main className={styles.mainContent}>
      <div className={styles.container}>
          {/* Project Details Section */}
          <section className={styles.projectDetails} id="projectDetailsSection">
            {project && (
              <ProjectDetails // Assuming ProjectDetails component handles its internal styles
                title={project.name ?? '專案名稱'}
                description={project.description ?? ''}
                status={
                  typeof project.project_status === 'string' ? project.project_status : 'active'
                }
                deadline={project.deadline ?? ''}
                highlights={
                  project.highlights ?? [
                    { icon: '✅', text: '品質保證' },
                    { icon: '🏡', text: '在地小農' },
                    { icon: '🚚', text: '安心配送' },
                  ]
                }
                images={project.images ?? []}
                message={message}
                messageType={messageType}
              />
            )}
          </section>
          {/* Items Selection Section */}
          <section className={styles.itemsContainer} id="itemsSelectionSection">
            <h2 className={styles.sectionTitle}>選擇商品</h2>
            <div className={styles.itemsList}>
              {items.map(item => (
                <ItemCard
                  key={item.id}
                  id={item.id}
                  name={item.name}
                  description={item.description ?? ''}
                  price={item.price}
                  quantity={cartItems.find(ci => ci.id === item.id)?.quantity ?? 0}
                  onQuantityChange={qty => handleQuantityChange(item.id, qty)}
                />
              ))}
            </div>
          </section>
          {/* Order Summary Section */}
          <section className={styles.orderSummary} id="orderSummarySection">
            <OrderSummary // Assuming OrderSummary component handles its internal styles
              cartItems={cartItems}
              subtotal={subtotal}
              discount={discount}
              total={total}
              remarks={remarks}
              pickupDate={pickupDate}
              onRemarksChange={setRemarks}
              onPickupDateChange={setPickupDate}
              onSubmit={handleSubmit}
              onRemoveItem={handleRemoveItem}
              appliedDiscount={appliedDiscount}
              submitDisabled={cartItems.length === 0 || isLoadingDiscount}
            />
          </section>
        </div>
      </main>
  );
};

export default PreorderPage;

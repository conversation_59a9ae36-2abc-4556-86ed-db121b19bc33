import React from 'react';
import styles from './styles.module.css';

/**
 * 圖標元件屬性介面
 * 定義所有圖標元件共用的屬性
 */
interface IconProps {
  /**
   * 自定義 CSS 類名
   * 可以用來覆蓋默認樣式或添加額外樣式
   */
  className?: string;
}

/**
 * 過濾圖標元件
 * 用於顯示過濾功能的圖標
 *
 * @param props - 圖標元件屬性
 * @returns 過濾圖標元件
 */
export const FilterIcon: React.FC<IconProps> = ({ className }) => (
  <svg
    className={`${styles.icon} ${className || ''}`}
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4 6H20M7 12H17M9 18H15"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

/**
 * 搜索圖標元件
 * 用於顯示搜索功能的圖標
 *
 * @param props - 圖標元件屬性
 * @returns 搜索圖標元件
 */
export const SearchIcon: React.FC<IconProps> = ({ className }) => (
  <svg
    className={`${styles.icon} ${className || ''}`}
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M21 21L16.65 16.65M19 11C19 15.4183 15.4183 19 11 19C6.58172 19 3 15.4183 3 11C3 6.58172 6.58172 3 11 3C15.4183 3 19 6.58172 19 11Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  TextField,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Chip,
  Stack,
  FormControl,
  InputLabel,
  SelectChangeEvent,
} from '@mui/material';
import {
  Search as SearchIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';

/**
 * 用戶資料介面
 */
interface User {
  id: string;
  name: string;
  email: string;
  role: 'user' | 'admin';
  createdAt: string;
  lastLogin: string;
}

/**
 * 用戶管理頁面
 * 提供用戶列表、搜尋、篩選和管理功能
 */
const UsersPage: React.FC = () => {
  // === 狀態管理 ===
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [users] = useState<User[]>([
    {
      id: '1',
      name: '王小明',
      email: '<EMAIL>',
      role: 'user',
      createdAt: '2024-03-15',
      lastLogin: '2024-03-20',
    },
    {
      id: '2',
      name: '李小華',
      email: '<EMAIL>',
      role: 'admin',
      createdAt: '2024-03-10',
      lastLogin: '2024-03-21',
    },
  ]);

  // === 事件處理 ===
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleRoleFilter = (event: SelectChangeEvent) => {
    setRoleFilter(event.target.value);
  };

  const handleView = (userId: string) => {
    console.log('查看用戶:', userId);
  };

  const handleEdit = (userId: string) => {
    console.log('編輯用戶:', userId);
  };

  // === 渲染方法 ===
  const renderRoleChip = (role: User['role']) => {
    return (
      <Chip
        label={role === 'admin' ? '管理員' : '一般用戶'}
        color={role === 'admin' ? 'primary' : 'default'}
        size="small"
      />
    );
  };

  return (
    <Box>
      {/* 頁面標題和工具列 */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          用戶管理
        </Typography>
        <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
          <TextField
            size="small"
            placeholder="搜尋用戶..."
            value={searchQuery}
            onChange={handleSearch}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
            }}
            sx={{ width: 250 }}
          />
          <FormControl size="small" sx={{ width: 150 }}>
            <InputLabel id="role-filter-label">角色</InputLabel>
            <Select
              labelId="role-filter-label"
              value={roleFilter}
              label="角色"
              onChange={handleRoleFilter}
            >
              <MenuItem value="all">全部角色</MenuItem>
              <MenuItem value="user">一般用戶</MenuItem>
              <MenuItem value="admin">管理員</MenuItem>
            </Select>
          </FormControl>
        </Stack>
      </Box>

      {/* 用戶列表 */}
      <TableContainer component={Paper} elevation={0} variant="outlined">
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>用戶名稱</TableCell>
              <TableCell>電子郵件</TableCell>
              <TableCell>角色</TableCell>
              <TableCell>註冊日期</TableCell>
              <TableCell>最後登入</TableCell>
              <TableCell align="right">操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map(user => (
              <TableRow key={user.id} hover>
                <TableCell>{user.name}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>{renderRoleChip(user.role)}</TableCell>
                <TableCell>{user.createdAt}</TableCell>
                <TableCell>{user.lastLogin}</TableCell>
                <TableCell align="right">
                  <Stack direction="row" spacing={1} justifyContent="flex-end">
                    <Button
                      size="small"
                      startIcon={<ViewIcon />}
                      onClick={() => handleView(user.id)}
                    >
                      查看
                    </Button>
                    <Button
                      size="small"
                      startIcon={<EditIcon />}
                      onClick={() => handleEdit(user.id)}
                    >
                      編輯
                    </Button>
                  </Stack>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default UsersPage;

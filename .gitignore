# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build
/dist
dist-ssr
*.local

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Misc
.vercel
.env*.local
*.pem
.eslintcache
.npm
.yarn-integrity

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Debug
.debug/
debug.log

# MCP Configuration
.cursor/

# IDE and Editor specific
.windsurfrules

# Project specific
prototypes/

# Cache
.cache/

# Repomix Configuration
.repomix/
.repomix.json
.repomix.js
.repomix.yaml
.repomix.yml

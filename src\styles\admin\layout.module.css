/* Admin backend sidebar & layout styles */

.sidebar {
  width: 250px;
  background-color: var(--bg-white);
  box-shadow: var(--shadow-md);
  padding: var(--space-lg) 0;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: 10;
}

.logo-container {
  padding: 0 var(--space-lg);
  margin-bottom: var(--space-xl);
  display: flex;
  align-items: center;
}

.logo {
  width: 40px;
  margin-right: var(--space-sm);
}

.logo-text {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.nav-title {
  padding: var(--space-sm) var(--space-lg);
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  color: var(--text-medium);
  text-decoration: none;
  transition: all 0.2s ease;
  margin-bottom: var(--space-xs);
}

.nav-item.active {
  background-color: var(--primary-light);
  color: var(--primary-color);
  font-weight: 500;
  border-right: 3px solid var(--primary-color);
}

.nav-item:hover:not(.active) {
  background-color: rgba(0, 0, 0, 0.03);
}

.nav-icon {
  margin-right: var(--space-md);
  width: 20px;
  height: 20px;
}

.main-content {
  flex: 1;
  margin-left: 250px;
  padding: var(--space-xl);
}

@media (max-width: 992px) {
  .sidebar {
    width: 200px;
  }
  .main-content {
    margin-left: 200px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  .sidebar.active {
    transform: translateX(0);
  }
  .main-content {
    margin-left: 0;
    padding: 1rem;
  }
}

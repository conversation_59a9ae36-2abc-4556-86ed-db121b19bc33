import React, { useState } from 'react';
import type { Order } from '../../../types/order';
import { OrderStatusBadge } from '../OrderStatusBadge';
import OrderDetailModal from '../OrderDetailModal';
import styles from './styles.module.css';

interface OrderTableProps {
  orders: Order[];
  loading?: boolean;
  className?: string;
  selectedIds: string[];
  onToggleSelect: (id: string) => void;
  onSelectAll: (checked: boolean) => void;
  onSaveOrder?: (updated: Partial<Order>) => Promise<void>;
}

export const OrderTable: React.FC<OrderTableProps> = ({
  orders,
  loading = false,
  className = '',
  selectedIds,
  onToggleSelect,
  onSelectAll,
  onSaveOrder,
}) => {
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  if (loading) {
    return <div className={`${styles.loading} ${className}`.trim()}>載入中...</div>;
  }

  if (orders.length === 0) {
    return <div className={`${styles.empty} ${className}`.trim()}>暫無訂單</div>;
  }

  const allSelected = orders.length > 0 && orders.every(o => selectedIds.includes(o.id));

  return (
    <div className={`${styles.tableWrapper} ${className}`.trim()}>
      <table className={styles.table}>
        <thead>
          <tr>
            <th>
              <input
                type="checkbox"
                checked={allSelected}
                onChange={e => onSelectAll(e.target.checked)}
              />
            </th>
            <th>訂單編號</th>
            <th>專案</th>
            <th>客戶</th>
            <th>日期</th>
            <th>金額</th>
            <th>折扣</th>
            <th>狀態</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          {orders.map(order => (
            <tr key={order.id}>
              <td>
                <input
                  type="checkbox"
                  checked={selectedIds.includes(order.id)}
                  onChange={() => onToggleSelect(order.id)}
                />
              </td>
              <td>{order.id}</td>
              <td>{order.projectName || '-'}</td>
              <td>
                <div className={styles.user}>
                  <div>{order.user.communityNickname || order.user.name}</div>
                  <div className={styles.subInfo}>LINE: {order.user.lineId || '-'}</div>
                </div>
              </td>
              <td>{new Date(order.createdAt ?? order.date ?? '').toLocaleDateString()}</td>
              <td>NT$ {order.total.toLocaleString()}</td>
              <td>-NT$ {order.discountAmount?.toLocaleString() || '0'}</td>
              <td>
                <OrderStatusBadge status={order.status} />
              </td>
              <td>
                <button className={styles.actionButton} onClick={() => setSelectedOrder(order)}>
                  詳情
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {selectedOrder && onSaveOrder && (
        <OrderDetailModal
          order={selectedOrder}
          onClose={() => setSelectedOrder(null)}
          onSave={onSaveOrder}
        />
      )}
    </div>
  );
};

export default OrderTable;

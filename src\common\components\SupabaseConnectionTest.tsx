import { useEffect, useState } from 'react';
import { checkSupabaseConnection } from '../../lib/supabase';

export const SupabaseConnectionTest = () => {
  const [connectionStatus, setConnectionStatus] = useState<{
    ok: boolean;
    message: string;
    error?: Error | null;
  }>({ ok: false, message: '檢查中...' });

  useEffect(() => {
    const testConnection = async () => {
      const result = await checkSupabaseConnection();
      setConnectionStatus(result);
    };

    testConnection();
  }, []);

  return (
    <div className="connection-test">
      <h2>Supabase 連接測試</h2>
      <div
        style={{
          padding: '15px',
          borderRadius: '4px',
          backgroundColor: connectionStatus.ok ? '#e6ffe6' : '#ffe6e6',
          marginTop: '10px',
        }}
      >
        <p>狀態: {connectionStatus.message}</p>
        {connectionStatus.error && (
          <pre
            style={{
              whiteSpace: 'pre-wrap',
              backgroundColor: '#f5f5f5',
              padding: '10px',
              borderRadius: '4px',
            }}
          >
            {JSON.stringify(connectionStatus.error, null, 2)}
          </pre>
        )}
      </div>
      <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
        <p>環境變數狀態：</p>
        <p>VITE_SUPABASE_URL: {import.meta.env.VITE_SUPABASE_URL ? '已設定' : '未設定'}</p>
        <p>
          VITE_SUPABASE_ANON_KEY: {import.meta.env.VITE_SUPABASE_ANON_KEY ? '已設定' : '未設定'}
        </p>
      </div>
    </div>
  );
};

/**
 * 小森活應用重置樣式
 * 確保所有瀏覽器的基礎樣式一致
 */

/* 重置所有元素的 margin 和 padding */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 設置 html 和 body 的基本樣式 */
html, body {
  min-height: 100vh;
  font-family: 'Noto Sans TC', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 移除列表樣式 */
ul, ol {
  list-style: none;
}

/* 移除超連結的默認樣式 */
a {
  text-decoration: none;
  color: inherit;
}

/* 重置按鈕樣式 */
button {
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
}

/* 重置輸入框樣式 */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
  border: none;
  outline: none;
}

/* 重置圖片樣式 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 重置表格樣式 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 移除 fieldset 的默認樣式 */
fieldset {
  border: none;
}

/* 確保 textarea 只能垂直調整大小 */
textarea {
  resize: vertical;
}

/* 移除 input[type="number"] 的上下箭頭 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield; /* 添加標準屬性以修復 lint 錯誤 */
}

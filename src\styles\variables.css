/**
 * 小森活應用全局 CSS 變數
 * 與 MUI 主題保持同步
 */

/* 前台主題變數 */
:root {
  /* ===== 顏色系統 ===== */
  
  /* 主色調 */
  --primary: #2c4f4f;
  --primary-light: #4c6e6e;
  --primary-dark: #1c3f3f;
  --primary-contrast: #ffffff;
  
  --secondary: #FFC75A;
  --secondary-light: #FFD78A;
  --secondary-dark: #DFA73A;
  --secondary-contrast: #704800;

  --success-color: #28a745;
  --success-light: rgba(40, 167, 69, 0.1);
  
  /* 背景色 */
  --background: #f5f5f5;
  --paper: #ffffff;
  
  /* 文字顏色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-disabled: #999999;
  
  /* 邊框顏色 */
  --border-color: #E0E0E0;
  --border-color-dark: #BDBDBD;
  
  /* ===== 佈局系統 ===== */
  
  /* 佈局尺寸 */
  --header-height: 60px;
  --footer-height: 56px;
  --container-width: 1200px;
  
  /* ===== 間距系統 ===== */
  --space-unit: 8px;
  --space-xs: calc(var(--space-unit) * 0.5);  /* 4px */
  --space-sm: var(--space-unit);               /* 8px */
  --space-md: calc(var(--space-unit) * 2);     /* 16px */
  --space-lg: calc(var(--space-unit) * 3);     /* 24px */
  --space-xl: calc(var(--space-unit) * 4);     /* 32px */
  
  /* ===== 字體系統 ===== */
  --font-family: "Noto Sans TC", "Roboto", "Helvetica", "Arial", sans-serif;
  
  /* 字體大小 */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.25rem;    /* 20px */
  --font-size-xl: 1.5rem;     /* 24px */
  --font-size-2xl: 2rem;      /* 32px */
  
  /* 字體粗細 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* ===== 特效系統 ===== */
  
  /* 圓角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 16px;
  --radius-full: 9999px;
  
  /* 陰影 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* 動畫 */
  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;
  --transition-slow: all 0.5s ease;
  
  /* ===== Z-index 系統 ===== */
  --z-negative: -1;
  --z-normal: 1;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* 後台主題變數 */
:root[data-theme="admin"] {
  /* ===== 顏色系統 ===== */
  
  /* 主色調 */
  --primary: #1976D2;
  --primary-light: #63A4FF;
  --primary-dark: #004BA0;
  --primary-contrast: #ffffff;
  
  --secondary: #2E7D32;
  --secondary-light: #60AD5E;
  --secondary-dark: #005005;
  --secondary-contrast: #ffffff;
  
  /* 狀態顏色 */
  --success: #2E7D32;
  --success-light: #60AD5E;
  --success-dark: #005005;
  
  --warning: #ED6C02;
  --warning-light: #FFB74D;
  --warning-dark: #E65100;
  
  --error: #D32F2F;
  --error-light: #EF5350;
  --error-dark: #C62828;
  
  --info: #0288D1;
  --info-light: #03A9F4;
  --info-dark: #01579B;
  
  /* 背景色 */
  --background: #f7f9fc;
  --paper: #ffffff;
  --sidebar: #1a1a1a;
  --nav-hover: #333333;
  --nav-active: #333333;
  
  /* 佈局尺寸 */
  --header-height: 64px;
  --footer-height: 48px;
  --sidebar-width: 280px;
}

/* 深色主題變數 */
:root[data-theme="dark"] {
  --background: #1E1E1E;
  --paper: #2D2D2D;
  --text-primary: #FFFFFF;
  --text-secondary: #B0B0B0;
  --border-color: #404040;
  --border-color-dark: #505050;
  
  /* 更新陰影效果以適應深色主題 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
}

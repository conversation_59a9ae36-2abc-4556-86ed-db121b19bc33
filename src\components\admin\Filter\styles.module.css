/* 引入後台變數 */
@import '../../../styles/admin-variables.css';

/* 篩選器容器 */
.filterContainer {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-md);
  padding: 1.5rem;
  background-color: var(--admin-bg-white);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-lg);
  box-shadow: var(--admin-shadow-sm);
}

/* 篩選器組 */
.filterGroup {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admin-space-md);
  align-items: flex-start;
}

/* 篩選器項目 */
.filterItem {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  min-width: 200px;
}

/* 標籤 */
.label {
  font-size: 0.9rem;
  font-weight: var(--admin-font-medium);
  color: #666;
}

/* 輸入框和選擇框基本樣式 */
.input,
.select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-md);
  font-size: 1rem;
  color: var(--admin-text-dark);
  background-color: var(--admin-bg-white);
  transition: var(--admin-transition-fast);
}

.select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px;
  padding-right: 2.5rem;
}

.input:hover,
.select:hover {
  border-color: var(--admin-primary);
}

.input:focus,
.select:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px var(--admin-primary-lighter);
}

/* 日期範圍容器 */
.dateRange {
  display: flex;
  gap: var(--admin-space-sm);
  align-items: center;
}

.dateRange span {
  color: var(--admin-text-medium);
  font-size: 0.9rem;
}

/* 操作按鈕容器 */
.actions {
  display: flex;
  gap: var(--admin-space-sm);
  margin-left: auto;
  align-self: flex-end;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .filterGroup {
    flex-direction: column;
  }

  .filterItem {
    width: 100%;
  }

  .actions {
    width: 100%;
    margin-left: 0;
    justify-content: flex-end;
  }
}

@media (max-width: 640px) {
  .dateRange {
    flex-direction: column;
    width: 100%;
  }

  .actions {
    flex-direction: column;
  }

  .actions button {
    width: 100%;
  }
} 
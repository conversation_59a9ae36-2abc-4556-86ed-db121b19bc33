import { useState, useCallback, useMemo, useEffect } from 'react';
import type { Order, OrderFilter, OrderPagination, UseOrderManagement } from '../types/order';
import { OrderStatus } from '../types/order';
import { supabase } from '../lib/supabase';

/**
 * 訂單管理 Hook
 *
 * 處理訂單列表的載入、篩選、分頁等邏輯
 *
 * @returns {UseOrderManagement} 訂單管理相關的狀態和方法
 */
export const useOrderManagement = (): UseOrderManagement => {
  const [loading, setLoading] = useState(true);

  const [orders, setOrders] = useState<Order[]>([]);

  const [filter, setFilter] = useState<OrderFilter>({
    status: undefined,
    searchQuery: '',
    projectId: undefined,
    dateStart: undefined,
    dateEnd: undefined,
    orderId: undefined,
    userName: undefined,
  });

  const [pagination, setPagination] = useState<OrderPagination>({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const fetchOrders = useCallback(async () => {
    setLoading(true);
    try {
      let query = supabase.from('orders').select(
        `
          id,
          order_number,
          created_at,
          total_amount,
          discount_amount,
          final_amount,
          status,
          project_id,
          project_name,
          user_id,
          user_name
        `,
        { count: 'exact' }
      );

      // 篩選條件
      if (filter.status) {
        query = query.eq('status', filter.status);
      }
      if (filter.projectId) {
        query = query.eq('project_id', filter.projectId);
      }
      if (filter.orderId) {
        query = query.ilike('order_number', `%${filter.orderId}%`);
      }
      if (filter.userName) {
        query = query.or(`user_name.ilike.%${filter.userName}%`);
      }
      if (filter.dateStart) {
        query = query.gte('created_at', filter.dateStart);
      }
      if (filter.dateEnd) {
        query = query.lte('created_at', filter.dateEnd);
      }
      if (filter.searchQuery) {
        query = query.or(
          `order_number.ilike.%${filter.searchQuery}%,user_name.ilike.%${filter.searchQuery}%`
        );
      }

      // 排序
      query = query.order('created_at', { ascending: false });

      // 分頁
      const { current, pageSize } = pagination;
      const offset = (current - 1) * pageSize;
      query = query.range(offset, offset + pageSize - 1);

      const { data, error, count } = await query;

      if (error) {
        console.error('獲取訂單失敗:', error);
        console.error('Supabase response data:', data);
        const { showError } = await import('../lib/showError');
        showError('訂單資料載入失敗，請稍後再試');
        return;
      }

      const transformedOrders: Order[] =
        data?.map((order: any) => ({
          id: order.order_number || order.id,
          user: {
            name: order.user_name || '',
            email: '',
            communityNickname: '',
            lineId: '',
          },
          date: order.created_at,
          total: order.total_amount,
          discountAmount: order.discount_amount,
          status: order.status,
          projectId: order.project_id,
          projectName: order.project_name || '',
          createdAt: order.created_at,
        })) ?? [];

      setOrders(transformedOrders);
      setPagination(prev => {
        if ((count ?? 0) !== prev.total) {
          return { ...prev, total: count ?? 0 };
        }
        return prev;
      });
    } catch (error) {
      console.error('獲取訂單失敗:', error);
      const { showError } = await import('../lib/showError');
      showError('訂單資料載入失敗，請稍後再試');
    } finally {
      setLoading(false);
    }
  }, [filter, pagination.current, pagination.pageSize]);

  const updateFilter = useCallback((newFilter: Partial<OrderFilter>) => {
    setFilter(prev => ({ ...prev, ...newFilter }));
    setPagination(prev => ({ ...prev, current: 1 }));
  }, []);

  const resetFilter = useCallback(() => {
    setFilter({
      status: undefined,
      searchQuery: '',
      projectId: undefined,
      dateStart: undefined,
      dateEnd: undefined,
      orderId: undefined,
      userName: undefined,
    });
    setPagination(prev => ({ ...prev, current: 1 }));
  }, []);

  const updatePage = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, current: page }));
  }, []);

  const reload = useCallback(async () => {
    await fetchOrders();
  }, [fetchOrders]);

  const batchUpdateStatus = async (selectedIds: string[], targetStatus: OrderStatus) => {
    if (selectedIds.length === 0) return;
    const { error } = await supabase
      .from('orders')
      .update({ status: targetStatus })
      .in('id', selectedIds);
    if (error) {
      console.error('批次更新訂單狀態失敗:', error);
      const { showError } = await import('../lib/showError');
      showError('批次更新訂單狀態失敗，請稍後再試');
    } else {
      await fetchOrders();
    }
  };

  const batchDelete = async (selectedIds: string[]) => {
    if (selectedIds.length === 0) return;
    const { error } = await supabase.from('orders').delete().in('id', selectedIds);
    if (error) {
      console.error('批次刪除訂單失敗:', error);
      const { showError } = await import('../lib/showError');
      showError('批次刪除訂單失敗，請稍後再試');
    } else {
      await fetchOrders();
    }
  };

  useEffect(() => {
    fetchOrders();
  }, [filter, pagination.current, pagination.pageSize]);

  const updateManualDiscount = async (
    orderId: string,
    manualDiscountAmount: number,
    adminId: string,
    adminNotes?: string
  ) => {
    const { data, error } = await supabase.rpc('update_order_manual_discount', {
      p_order_id: orderId,
      p_manual_discount_amount: manualDiscountAmount,
      p_admin_id: adminId,
      p_admin_notes: adminNotes ?? null,
    });

    if (error) {
      console.error('更新手動折扣失敗:', error);
      const { showError } = await import('../lib/showError');
      showError('更新手動折扣失敗，請稍後再試');
      throw error;
    }

    return data;
  };

  return {
    orders,
    loading,
    filter,
    pagination,
    updateFilter,
    resetFilter,
    updatePage,
    reload,
    batchUpdateStatus,
    batchDelete,
    updateManualDiscount,
  };
};

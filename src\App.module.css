.app {
  text-align: center;
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.header {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-top: var(--spacing-xl);
  font-size: calc(10px + 2vmin);
  color: var(--text-primary);
}

.title {
  margin-bottom: var(--spacing-xl);
  color: var(--primary-dark);
}

.connectionTest {
  margin-top: var(--spacing-xl);
  width: 100%;
  max-width: 600px;
} 
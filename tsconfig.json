{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    
    /* Type Definitions */
    "types": ["node", "jest"],
    
    /* CSS Modules */
    "plugins": [
      { "name": "typescript-plugin-css-modules" }
    ],

    "baseUrl": "./src",
    "paths": {
      "@/*": ["./*"],
      "@components/user": ["components/user/index.ts"],
      "components/*": ["components/*"],
      "lib/*": ["lib/*"],
      "hooks/*": ["hooks/*"],
      "pages/*": ["pages/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}

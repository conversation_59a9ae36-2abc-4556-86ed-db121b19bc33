import { CartItem } from '../types/cart';
import { Discount } from '../types/discount';

export interface DiscountCalculationResult {
  discountId: string | null;
  discountAmount: number;
  discountDetails: {
    name: string;
    description: string | null;
    applicableQuantity: number;
    thresholdQuantity: number;
    discountPerItem: number;
    totalDiscount: number;
    applicableSubtotal: number;
  } | null;
}

/**
 * 計算單個折扣規則的折扣金額
 * @param cartItems 購物車項目
 * @param discount 折扣規則
 * @returns 折扣計算結果
 */
export const calculateQuantityTierDiscount = (
  cartItems: CartItem[],
  discount: Discount
): DiscountCalculationResult => {
  let applicableQuantity = 0;
  let applicableSubtotal = 0;

  // 計算適用商品的數量和小計
  cartItems.forEach(item => {
    if (!discount.applicableItemIds?.length || discount.applicableItemIds.includes(item.itemId)) {
      applicableQuantity += item.quantity;
      applicableSubtotal += item.subtotal;
    }
  });

  // 檢查是否達到門檻
  if (applicableQuantity >= discount.quantityThreshold) {
    const totalDiscount = Math.min(
      applicableQuantity * discount.discountPerItem,
      applicableSubtotal
    );

    return {
      discountId: discount.id,
      discountAmount: totalDiscount,
      discountDetails: {
        name: discount.name,
        description: discount.description,
        applicableQuantity,
        thresholdQuantity: discount.quantityThreshold,
        discountPerItem: discount.discountPerItem,
        totalDiscount,
        applicableSubtotal,
      },
    };
  }

  return {
    discountId: null,
    discountAmount: 0,
    discountDetails: null,
  };
};

/**
 * 計算多個折扣規則中的最佳折扣
 * @param cartItems 購物車項目
 * @param discounts 折扣規則列表
 * @returns 最佳折扣計算結果
 */
export const calculateBestDiscount = (
  cartItems: CartItem[],
  discounts: Discount[]
): DiscountCalculationResult => {
  if (!cartItems.length || !discounts.length) {
    return {
      discountId: null,
      discountAmount: 0,
      discountDetails: null,
    };
  }

  let bestDiscount: DiscountCalculationResult = {
    discountId: null,
    discountAmount: 0,
    discountDetails: null,
  };

  discounts.forEach(discount => {
    const result = calculateQuantityTierDiscount(cartItems, discount);
    if (result.discountAmount > bestDiscount.discountAmount) {
      bestDiscount = result;
    }
  });

  return bestDiscount;
};

/**
 * 格式化折扣顯示文字
 * @param result 折扣計算結果
 * @returns 格式化的折扣說明文字
 */
export const formatDiscountDescription = (result: DiscountCalculationResult): string => {
  if (!result.discountDetails) {
    return '未符合任何折扣條件';
  }

  const { name, applicableQuantity, thresholdQuantity, discountPerItem, totalDiscount } =
    result.discountDetails;

  return `${name}: 已購買 ${applicableQuantity} 件商品，達到 ${thresholdQuantity} 件門檻，每件折抵 ${discountPerItem} 元，共折抵 ${totalDiscount} 元`;
};

import React from 'react';
import './SearchBar.css';

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onFilterClick?: () => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ value, onChange, onFilterClick }) => {
  return (
    <div className="search-container">
      <input
        type="text"
        className="form-input search-input"
        placeholder="搜尋專案..."
        aria-label="搜尋專案"
        value={value}
        onChange={e => onChange(e.target.value)}
      />
      <button className="filter-btn" aria-label="篩選" onClick={onFilterClick}>
        <span className="nav-icon search-icon"></span>
      </button>
    </div>
  );
};

export default SearchBar;

import React from 'react';
import { HomeIcon, CartIcon, OrderIcon } from '../../icons/NavIcons';
import styles from './styles.module.css';

/**
 * 導航項目介面
 * 定義底部導航欄中每個導航項目的屬性
 */
export interface NavItem {
  /**
   * 導航項目的唯一標識
   * 用於識別當前活動項目和處理點擊事件
   */
  id: string;
  /**
   * 導航項目的標籤文字
   * 顯示在圖標下方
   */
  label: string;
  /**
   * 導航項目的圖標名稱
   * 目前支持: 'home', 'cart', 'order'
   */
  iconName: string;
  /**
   * 導航項目的連結
   * 用於直接導航（如果未提供 onItemClick 回調）
   */
  link: string;
}

/**
 * 底部導航元件屬性介面
 */
interface BottomNavProps {
  /**
   * 導航項目列表
   * 可以使用 defaultNavItems 或 adminNavItems 或自定義項目列表
   * @see src/components/layout/nav-items.ts
   */
  items: NavItem[];
  /**
   * 當前活動的導航項目 ID
   * 應與 NavItem.id 匹配
   */
  activeItemId: string;
  /**
   * 點擊導航項目時的回調函數
   * 如果提供此回調，點擊導航項目時將不會直接導航，而是調用此回調
   * @param itemId 被點擊的導航項目 ID
   */
  onItemClick?: (itemId: string) => void;
}

/**
 * 根據圖標名稱返回對應的圖標組件
 *
 * @param iconName 圖標名稱，目前支持: 'home', 'cart', 'order'
 * @param isActive 圖標是否處於活動狀態
 * @returns 對應的圖標 React 元素
 */
const getIconComponent = (iconName: string, isActive: boolean): React.ReactNode => {
  const className = `${styles.navIcon} ${isActive ? styles.active : ''}`;
  switch (iconName) {
    case 'home':
      return <HomeIcon className={className} />;
    case 'cart':
      return <CartIcon className={className} />;
    case 'order':
      return <OrderIcon className={className} />;
    default:
      return null;
  }
};

/**
 * 底部導航元件 - 顯示在移動端頁面底部的導航欄
 *
 * 使用方式:
 * ```tsx
 * import { BottomNav } from '../components';
 * import { defaultNavItems } from '../components/layout/nav-items';
 *
 * <BottomNav
 *   items={defaultNavItems}
 *   activeItemId="home"
 *   onItemClick={handleNavItemClick}
 * />
 * ```
 *
 * 樣式使用 CSS Module 確保樣式隔離，避免樣式衝突
 *
 * @param props 底部導航元件屬性
 * @returns 底部導航元件
 */
const BottomNav: React.FC<BottomNavProps> = ({ items, activeItemId, onItemClick }) => {
  /**
   * 處理導航項目點擊
   *
   * @param itemId 被點擊的導航項目 ID
   */
  const handleItemClick = (itemId: string): void => {
    if (onItemClick) {
      onItemClick(itemId);
    }
  };

  return (
    <nav className={styles.bottomNav}>
      {items.map(item => (
        <a
          key={item.id}
          href={item.link}
          className={`${styles.navItem} ${item.id === activeItemId ? styles.active : ''}`}
          onClick={(e: React.MouseEvent<HTMLAnchorElement>): void => {
            if (onItemClick) {
              e.preventDefault();
              handleItemClick(item.id);
            }
          }}
        >
          {getIconComponent(item.iconName, item.id === activeItemId)}
          <span className={styles.navLabel}>{item.label}</span>
        </a>
      ))}
    </nav>
  );
};

export default BottomNav;

import React from 'react';
import { Tag } from 'antd';
import { OrderStatus } from '../../../types/order';

interface OrderStatusBadgeProps {
  status: OrderStatus;
  className?: string;
}

export const OrderStatusBadge: React.FC<OrderStatusBadgeProps> = ({ status, className = '' }) => {
  const statusMap = {
    [OrderStatus.Pending]: { color: 'orange', label: '待處理' },
    [OrderStatus.Processing]: { color: 'blue', label: '處理中' },
    [OrderStatus.Completed]: { color: 'green', label: '已完成' },
    [OrderStatus.Cancelled]: { color: 'red', label: '已取消' },
  };

  const { color, label } = statusMap[status] || { color: 'default', label: status };

  return (
    <Tag color={color} className={className}>
      {label}
    </Tag>
  );
};

export default OrderStatusBadge;

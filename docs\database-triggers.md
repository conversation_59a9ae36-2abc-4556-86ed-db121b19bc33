# 資料庫觸發器與自動化機制說明文檔

## 專案狀態自動更新機制

### 資料表結構

#### project_status_logs 表
用於記錄專案狀態變更歷史：
```sql
CREATE TABLE public.project_status_logs (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id uuid REFERENCES public.projects(id),
    old_status text,
    new_status text,
    change_reason text,
    changed_at timestamp with time zone DEFAULT now()
);
```

### 觸發器函數

#### update_project_status()
```sql
CREATE OR REPLACE FUNCTION update_project_status()
RETURNS TRIGGER AS $$
DECLARE
    status_changed boolean := false;
    change_reason text;
BEGIN
    -- 如果已經是 completed 狀態，不做任何更新
    IF OLD.project_status = 'completed' THEN
        RETURN OLD;
    END IF;

    -- 保存原始狀態用於比較
    NEW.project_status = OLD.project_status;

    -- 根據時間順序檢查並更新狀態
    IF OLD.project_status = 'Active' AND 
       NEW.deadline IS NOT NULL AND 
       NOW() >= NEW.deadline THEN
        NEW.project_status = 'ordering_ended';
        change_reason = 'Deadline reached';
        status_changed := true;
    END IF;

    IF (OLD.project_status = 'Active' OR OLD.project_status = 'ordering_ended') AND 
       NEW.arrival_date IS NOT NULL AND 
       NOW() >= NEW.arrival_date THEN
        NEW.project_status = 'arrived';
        change_reason = 'Arrival date reached';
        status_changed := true;
    END IF;

    -- 如果狀態有變更，記錄到日誌
    IF status_changed THEN
        INSERT INTO public.project_status_logs 
            (project_id, old_status, new_status, change_reason)
        VALUES 
            (NEW.id, OLD.project_status, NEW.project_status, change_reason);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### 觸發器
```sql
CREATE TRIGGER auto_update_project_status
    BEFORE UPDATE
    ON public.projects
    FOR EACH ROW
    EXECUTE FUNCTION update_project_status();
```

### 定時檢查函數
```sql
CREATE OR REPLACE FUNCTION check_and_update_project_status()
RETURNS void AS $$
DECLARE
    project_record record;
BEGIN
    FOR project_record IN 
        SELECT * FROM public.projects 
        WHERE project_status != 'completed'
    LOOP
        -- 觸發更新檢查
        UPDATE public.projects
        SET updated_at = NOW()
        WHERE id = project_record.id;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

## 狀態轉換規則

### 專案狀態（project_status）可能的值：
1. **Active**：進行中，可以預購
   - 初始狀態
   - 用戶可以瀏覽商品並下單
   - 管理員可以編輯所有內容

2. **ordering_ended**：結束預購，等待到貨
   - 當前時間超過 deadline 時自動更新
   - 用戶不可再下新訂單
   - 管理員可以查看訂單統計

3. **arrived**：已到貨，可以取貨
   - 當前時間超過 arrival_date 時自動更新
   - 系統自動發送到貨通知
   - 用戶可以查看取貨資訊

4. **completed**：已完成
   - 只能由管理員手動設定
   - 設定為此狀態後不會再自動更新

### 狀態轉換邏輯
1. 狀態轉換順序：
   - Active -> ordering_ended -> arrived -> completed
   - 不允許跳躍式轉換（例如：不能從 Active 直接到 arrived）
   - completed 狀態為終態，不可再變更

2. 自動轉換條件：
   - Active -> ordering_ended：當前時間 >= deadline
   - ordering_ended -> arrived：當前時間 >= arrival_date

3. 手動轉換限制：
   - 只有管理員可以手動設定狀態為 completed
   - 其他狀態變更需遵循自動轉換邏輯

## 使用說明

### 自動更新機制
1. 觸發器會在以下情況自動執行：
   - 更新專案資料時
   - 特別是更新 deadline 或 arrival_date 時

2. 定時檢查：
   - 使用 check_and_update_project_status() 函數
   - 建議設定為每小時執行一次
   - 可通過 cron job 或排程工具執行

### 狀態變更日誌
1. 所有狀態變更都會記錄在 project_status_logs 表中
2. 記錄內容包括：
   - 變更前後的狀態
   - 變更原因
   - 變更時間
   - 相關專案 ID

## 注意事項
1. 狀態更新具有順序性，確保狀態轉換符合業務邏輯
2. completed 狀態為終態，設定後不可再更改
3. 自動更新機制不影響管理員的手動操作權限
4. 所有狀態變更都有完整的日誌記錄，方便追蹤和稽核 
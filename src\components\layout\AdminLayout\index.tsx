import React, { useState, useEffect } from 'react';
import { Link as RouterLink, useLocation, Outlet, useNavigate } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  IconButton,
  Typography,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Container,
  useTheme,
  Link,
  CircularProgress,
  Button,
  Alert,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  Folder as ProjectIcon,
  ShoppingCart as OrderIcon,
  People as UserIcon,
  LocalOffer as DiscountIcon,
  Home as HomeIcon,
  Logout as LogoutIcon,
} from '@mui/icons-material';
import { supabase } from '@/lib/supabase';
import '@/styles/admin-variables.css'; // 導入 Admin 專用變數

/**
 * 管理後台佈局元件的屬性介面
 */
interface AdminLayoutProps {
  readonly isFullWidth?: boolean;
}

/**
 * 管理後台導航項目定義
 */
const navItems = [
  { path: '/admin', icon: <DashboardIcon />, label: '儀表板' },
  { path: '/admin/projects', icon: <ProjectIcon />, label: '專案管理' },
  { path: '/admin/orders', icon: <OrderIcon />, label: '訂單管理' },
  { path: '/admin/users', icon: <UserIcon />, label: '用戶管理' },
  { path: '/admin/discounts', icon: <DiscountIcon />, label: '折扣管理' },
];

/**
 * 管理後台佈局元件
 *
 * 提供管理後台的側邊欄導航和內容區域佈局
 * 支援響應式設計和滿版內容顯示
 *
 * @param isFullWidth - 是否使用滿版佈局
 * @returns 管理後台佈局元件
 */
const AdminLayout: React.FC<AdminLayoutProps> = ({ isFullWidth = false }) => {
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        setError(null);

        // 檢查本地存儲的登入狀態
        const isLoggedIn = localStorage.getItem('isAdminLoggedIn') === 'true';
        const userId = localStorage.getItem('adminUserId');
        const role = localStorage.getItem('adminRole');

        if (!isLoggedIn || !userId || !['admin', 'manager'].includes(role || '')) {
          throw new Error('未登入或權限不足');
        }

        // 驗證用戶是否存在且有權限
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id, role, display_name, community_nickname, last_login_at')
          .eq('id', userId)
          .single();

        if (userError) {
          if (userError.code === 'PGRST116') {
            throw new Error('用戶不存在');
          }
          throw userError;
        }

        if (!userData || !['admin', 'manager'].includes(userData.role)) {
          throw new Error('用戶權限不足');
        }

        // 更新最後登入時間
        const { error: updateError } = await supabase
          .from('users')
          .update({ last_login_at: new Date().toISOString() })
          .eq('id', userId);

        if (updateError) {
          console.error('Failed to update last login time:', updateError);
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Auth check failed:', err);
        setError(err instanceof Error ? err.message : '驗證失敗');
        localStorage.removeItem('isAdminLoggedIn');
        localStorage.removeItem('adminUserId');
        localStorage.removeItem('adminRole');
        localStorage.removeItem('adminDisplayName');
        navigate('/admin/login');
      }
    };

    checkAuth();
  }, [navigate]);

  const handleDrawerToggle = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleLogout = () => {
    localStorage.removeItem('isAdminLoggedIn');
    localStorage.removeItem('adminUserId');
    localStorage.removeItem('adminRole');
    localStorage.removeItem('adminDisplayName');
    navigate('/admin/login');
  };

  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  const drawer = (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Box
        sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          borderBottom: 1,
          borderColor: 'divider',
        }}
      >
        <img
          src="/assets/images/forest-life-logo.png"
          alt="小森活"
          style={{ width: 40, height: 40 }}
        />
        <Box>
          <Typography variant="h6" noWrap>
            小森活
          </Typography>
          <Typography variant="body2" color="text.secondary">
            管理後台
          </Typography>
        </Box>
      </Box>

      <List sx={{ flex: 1, pt: 2 }}>
        {navItems.map(({ path, icon, label }, index) => (
          <ListItem key={path} disablePadding>
            <ListItemButton
              component={RouterLink}
              to={path}
              selected={location.pathname === path}
              autoFocus={index === 0}
              sx={{
                '&.Mui-selected': {
                  backgroundColor: 'primary.light',
                  '&:hover': {
                    backgroundColor: 'primary.light',
                  },
                },
              }}
            >
              <ListItemIcon sx={{ color: location.pathname === path ? 'primary.main' : 'inherit' }}>
                {icon}
              </ListItemIcon>
              <ListItemText primary={label} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Link
          component={RouterLink}
          to="/"
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            color: 'text.secondary',
            textDecoration: 'none',
            '&:hover': {
              color: 'primary.main',
            },
          }}
        >
          <HomeIcon />
          <Typography>返回前台</Typography>
        </Link>
        <Button
          onClick={handleLogout}
          startIcon={<LogoutIcon />}
          sx={{
            mt: 2,
            width: '100%',
          }}
          color="error"
          variant="outlined"
        >
          登出
        </Button>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {error && (
        <Alert
          severity="error"
          sx={{
            position: 'fixed',
            top: 16,
            right: 16,
            zIndex: theme.zIndex.drawer + 2,
          }}
        >
          {error}
        </Alert>
      )}

      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${240}px)` },
          ml: { sm: `${240}px` },
          display: { sm: 'none' },
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div">
            小森活管理後台
          </Typography>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{
          width: { sm: 240 },
          flexShrink: { sm: 0 },
        }}
      >
        <Drawer
          variant="temporary"
          open={isSidebarOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
            disableEnforceFocus: false,
            disableRestoreFocus: false,
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: 240,
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: 240,
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${240}px)` },
          minHeight: '100vh',
          bgcolor: 'background.default',
        }}
      >
        <Toolbar sx={{ display: { sm: 'none' } }} />
        {isFullWidth ? (
          <Outlet />
        ) : (
          <Container maxWidth="lg">
            <Outlet />
          </Container>
        )}
      </Box>
    </Box>
  );
};

export default AdminLayout;

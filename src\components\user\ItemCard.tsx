import React from 'react';
import './ItemCard.css';

interface ItemCardProps {
  id: string;
  name: string;
  description: string;
  price: number;
  quantity: number;
  onQuantityChange: (quantity: number) => void;
}

const ItemCard: React.FC<ItemCardProps> = ({
  id,
  name,
  description,
  price,
  quantity,
  onQuantityChange,
}) => {
  return (
    <div className="item-card" data-item-id={id}>
      <div className="item-info">
        <h3 className="item-name">{name}</h3>
        <p className="item-description">{description}</p>
      </div>
      <div className="item-price">NT$ {price.toLocaleString()}</div>
      <div className="quantity-control">
        <button
          className="quantity-btn decrease-qty"
          aria-label="減少數量"
          onClick={() => onQuantityChange(Math.max(0, quantity - 1))}
        >
          -
        </button>
        <input
          type="number"
          className="quantity-input"
          value={quantity}
          min={0}
          onChange={e => onQuantityChange(Math.max(0, Number(e.target.value)))}
        />
        <button
          className="quantity-btn increase-qty"
          aria-label="增加數量"
          onClick={() => onQuantityChange(quantity + 1)}
        >
          +
        </button>
      </div>
    </div>
  );
};

export default ItemCard;

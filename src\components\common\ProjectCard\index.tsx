import React from 'react';
import type { Project } from '../../types';
import styles from './styles.module.css';

/**
 * 專案卡片元件屬性介面
 * 定義專案卡片元件的所有可配置屬性
 */
interface ProjectCardProps {
  /**
   * 專案資料
   */
  readonly project: Project;
  /**
   * 點擊專案卡片時的回調函數
   * 當用戶點擊卡片時觸發，傳入專案 ID
   */
  readonly onClick?: (projectId: string) => void;
}

/**
 * 專案卡片元件
 *
 * 用於顯示專案資訊，包括專案圖片、名稱、描述、狀態和特點標籤等。
 * 支持點擊卡片觸發回調函數，適用於專案列表頁面。
 * 根據專案狀態顯示不同的狀態標籤樣式。
 *
 * 使用示例:
 * ```tsx
 * <ProjectCard
 *   project={projectData}
 *   onClick={handleProjectClick}
 * />
 * ```
 *
 * @param props - 專案卡片元件屬性
 * @returns 專案卡片元件
 */
const ProjectCard: React.FC<ProjectCardProps> = ({ project, onClick }) => {
  /**
   * 格式化日期顯示
   */
  const formatDate = (date?: Date): string => {
    if (!date) return '無截止日期';
    return date.toLocaleDateString('zh-TW', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  /**
   * 處理卡片點擊
   */
  const handleClick = (): void => {
    if (onClick) {
      onClick(project.id);
    }
  };

  /**
   * 獲取狀態標籤文字
   */
  const getStatusText = (status: Project['status']): string => {
    const statusMap: Record<Project['status'], string> = {
      pending: '即將開始',
      active: '進行中',
      ordering_ended: '訂購結束',
      arrived: '已到貨',
      completed: '已結束',
    };
    return statusMap[status] || '未知狀態';
  };

  /**
   * 獲取狀態標籤樣式類名
   */
  const getStatusClassName = (status: Project['status']): string => {
    const statusClassMap: Record<string, string> = {
      pending: styles.statusPending,
      active: styles.statusActive,
      ordering_ended: styles.statusPending,
      arrived: styles.statusActive,
      completed: styles.statusCompleted,
    };
    return statusClassMap[status] || '';
  };

  return (
    <div
      className={styles.card}
      onClick={handleClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
    >
      {project.images && project.images.length > 0 && (
        <div className={styles.imageContainer}>
          <img src={project.images[0]} alt={project.name} className={styles.image} />
          <div className={`${styles.status} ${getStatusClassName(project.status)}`}>
            {getStatusText(project.status)}
          </div>
        </div>
      )}

      <div className={styles.content}>
        <h3 className={styles.title}>{project.name}</h3>

        {project.deadline && (
          <div className={styles.deadline}>截止日期：{formatDate(project.deadline)}</div>
        )}

        <p className={styles.description}>{project.description}</p>

        {project.highlights && project.highlights.length > 0 && (
          <div className={styles.highlights}>
            {project.highlights.map((highlight, index) => (
              <div key={index} className={styles.highlightItem}>
                <div className={styles.highlightIcon}>{highlight.icon}</div>
                <span className={styles.highlightText}>{highlight.text}</span>
              </div>
            ))}
          </div>
        )}

        <div className={styles.action}>
          <button className="btn btn-primary">查看詳情</button>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;

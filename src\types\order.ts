export enum OrderStatus {
  Pending = 'Pending',
  Confirmed = 'Confirmed',
  Cancelled = 'Cancelled',
  Completed = 'Completed',
}

export interface Order {
  id: string;
  userId: string;
  projectId: string;
  projectName?: string;
  user: {
    id: string;
    name: string;
    email?: string;
    lineId?: string;
    communityNickname?: string;
  };
  createdAt: string;
  date?: string;
  total: number;
  discountAmount?: number;
  status: string;

  manualDiscountAmount?: number | null;
  adminNotes?: string | null;
  manualDiscountAppliedBy?: string | null;
  manualDiscountAppliedAt?: string | null;
  finalAmount?: number | null;
}

import React from 'react';
import { Button, ButtonGroup } from '../../common/Button';
import styles from './styles.module.css';

export interface FilterOption {
  label: string;
  value: string | number;
}

export interface FilterItem {
  key: string;
  label: string;
  type: 'input' | 'select' | 'dateRange';
  options?: FilterOption[];
  placeholder?: string;
}

export interface FilterProps {
  items: FilterItem[];
  values: Record<string, any>;
  onChange: (key: string, value: any) => void;
  onReset: () => void;
  onSubmit: () => void;
  loading?: boolean;
}

export const Filter: React.FC<FilterProps> = ({
  items,
  values,
  onChange,
  onReset,
  onSubmit,
  loading = false,
}) => {
  const renderFilterItem = (item: FilterItem) => {
    switch (item.type) {
      case 'select':
        return (
          <select
            className={styles.select}
            value={values[item.key] || ''}
            onChange={e => onChange(item.key, e.target.value)}
          >
            <option value="">{item.placeholder || '請選擇'}</option>
            {item.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'dateRange':
        return (
          <div className={styles.dateRange}>
            <input
              type="date"
              className={styles.input}
              value={values[`${item.key}Start`] || ''}
              onChange={e => onChange(`${item.key}Start`, e.target.value)}
            />
            <span>至</span>
            <input
              type="date"
              className={styles.input}
              value={values[`${item.key}End`] || ''}
              onChange={e => onChange(`${item.key}End`, e.target.value)}
            />
          </div>
        );

      default:
        return (
          <input
            type="text"
            className={styles.input}
            value={values[item.key] || ''}
            placeholder={item.placeholder}
            onChange={e => onChange(item.key, e.target.value)}
          />
        );
    }
  };

  return (
    <div className={styles.filterContainer}>
      <div className={styles.filterGroup}>
        {items.map(item => (
          <div key={item.key} className={styles.filterItem}>
            <label className={styles.label}>{item.label}</label>
            {renderFilterItem(item)}
          </div>
        ))}
        <div className={styles.actions}>
          <ButtonGroup>
            <Button variant="outlined" onClick={onReset} disabled={loading}>
              重置
            </Button>
            <Button onClick={onSubmit} loading={loading}>
              篩選
            </Button>
          </ButtonGroup>
        </div>
      </div>
    </div>
  );
};

export default Filter;

import { User } from './models';

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

export interface AuthContextType extends AuthState {
  login: () => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
}

export interface LiffProfile {
  userId: string;
  displayName: string;
  pictureUrl?: string;
  statusMessage?: string;
}

export interface AdminLoginState {
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AdminAuthContextType extends AdminLoginState {
  login: (userId: string) => Promise<void>;
  logout: () => Promise<void>;
}

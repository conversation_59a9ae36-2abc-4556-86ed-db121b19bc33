import React, { useEffect, useState } from 'react';
import { supabase } from '../../../lib/supabase';
import { useProjects } from '../ProjectsPage/hooks/useProjects';
import Button from '../../../components/common/Button';
import Table from '../../../components/admin/Table';
import styles from '../DiscountsPage/styles.module.css';
import { useNavigate } from 'react-router-dom';

interface Discount {
  id: string;
  name: string;
}

interface Mapping {
  id: string;
  discount_id: string;
  project_id: string;
}

const DiscountProjectMappingPage: React.FC = () => {
  const navigate = useNavigate();

  const [discounts, setDiscounts] = useState<Discount[]>([]);
  const [selectedDiscount, setSelectedDiscount] = useState<Discount | null>(null);
  const [mappings, setMappings] = useState<Mapping[]>([]);
  const [selectedProjectIds, setSelectedProjectIds] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  const { projects, isLoading: projectsLoading } = useProjects();

  useEffect(() => {
    const fetchDiscounts = async () => {
      setLoading(true);
      const { data, error } = await supabase
        .from('discounts')
        .select('id, name')
        .order('created_at', { ascending: false });
      if (error) {
        alert('載入折扣失敗');
        console.error(error);
      } else {
        setDiscounts(data ?? []);
      }
      setLoading(false);
    };
    fetchDiscounts();
  }, []);

  useEffect(() => {
    const fetchMappings = async () => {
      if (!selectedDiscount) {
        setMappings([]);
        return;
      }
      setLoading(true);
      const { data, error } = await supabase
        .from('discount_project_mappings')
        .select('id, discount_id, project_id')
        .eq('discount_id', selectedDiscount.id);
      if (error) {
        alert('載入映射失敗');
        console.error(error);
      } else {
        setMappings(data ?? []);
      }
      setLoading(false);
    };
    fetchMappings();
  }, [selectedDiscount]);

  const handleAddMappings = async () => {
    if (!selectedDiscount || selectedProjectIds.length === 0) return;
    setSaving(true);
    const inserts = selectedProjectIds.map(pid => ({
      discount_id: selectedDiscount.id,
      project_id: pid,
    }));
    const { error } = await supabase
      .from('discount_project_mappings')
      .upsert(inserts, { onConflict: 'discount_id,project_id' });
    if (error) {
      alert('新增映射失敗');
      console.error(error);
    } else {
      alert('新增映射成功');
      setSelectedProjectIds([]);
      const { data } = await supabase
        .from('discount_project_mappings')
        .select('id, discount_id, project_id')
        .eq('discount_id', selectedDiscount.id);
      setMappings(data ?? []);
    }
    setSaving(false);
  };

  const handleDeleteMapping = async (mappingId: string) => {
    if (!window.confirm('確定要刪除這個映射嗎？')) return;
    setSaving(true);
    const { error } = await supabase.from('discount_project_mappings').delete().eq('id', mappingId);
    if (error) {
      alert('刪除映射失敗');
      console.error(error);
    } else {
      alert('刪除成功');
      setMappings(prev => prev.filter(m => m.id !== mappingId));
    }
    setSaving(false);
  };

  const columns = [
    {
      key: 'project',
      title: '專案名稱',
      render: (_: any, record: Mapping) => {
        const project = projects.find(p => p.id === record.project_id);
        return project ? project.name : record.project_id;
      },
    },
    {
      key: 'actions',
      title: '操作',
      render: (_: any, record: Mapping) => (
        <Button
          size="small"
          color="error"
          variant="outlined"
          onClick={() => handleDeleteMapping(record.id)}
          disabled={saving}
        >
          移除
        </Button>
      ),
    },
  ];

  return (
    <div style={{ padding: '2rem', background: '#f5f7fa', minHeight: '100vh' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '1.5rem' }}>
        <h1 style={{ margin: 0 }}>折扣專案映射管理</h1>
        <Button variant="outlined" onClick={() => navigate('/admin/discounts')}>
          返回折扣管理
        </Button>
      </div>

      <div style={{ display: 'flex', gap: '2rem', alignItems: 'flex-start' }}>
        {/* 折扣列表 */}
        <div
          style={{
            flex: '0 0 220px',
            background: '#fff',
            borderRadius: '8px',
            padding: '1rem',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          }}
        >
          <h2 style={{ fontSize: '1.2rem', marginBottom: '1rem' }}>折扣列表</h2>
          {loading ? (
            <p>載入中...</p>
          ) : (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              {discounts.map(d => (
                <Button
                  key={d.id}
                  variant={selectedDiscount?.id === d.id ? 'contained' : 'outlined'}
                  size="small"
                  onClick={() => setSelectedDiscount(d)}
                  style={{ justifyContent: 'flex-start' }}
                >
                  {d.name || '(未命名)'}
                </Button>
              ))}
            </div>
          )}
        </div>

        {/* 右側內容 */}
        <div
          style={{
            flex: 1,
            background: '#fff',
            borderRadius: '8px',
            padding: '1.5rem',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            display: 'flex',
            flexDirection: 'column',
            gap: '1.5rem',
          }}
        >
          {selectedDiscount ? (
            <>
              <div>
                <h2 style={{ fontSize: '1.3rem', marginBottom: '1rem' }}>
                  折扣「{selectedDiscount.name}」的專案映射
                </h2>
                <Table columns={columns} data={mappings} loading={loading || projectsLoading} />
              </div>

              <div>
                <h3 style={{ fontSize: '1.1rem', marginBottom: '0.5rem' }}>新增關聯</h3>
                {projectsLoading ? (
                  <p>載入專案中...</p>
                ) : (
                  <>
                    <select
                      multiple
                      value={selectedProjectIds}
                      onChange={e => {
                        const options = Array.from(e.target.selectedOptions).map(o => o.value);
                        setSelectedProjectIds(options);
                      }}
                      style={{
                        width: '100%',
                        minHeight: '150px',
                        marginBottom: '1rem',
                        borderRadius: '4px',
                        border: '1px solid #ccc',
                        padding: '0.5rem',
                        background: '#fafafa',
                      }}
                    >
                      {projects.map(p => (
                        <option key={p.id} value={p.id}>
                          {p.name}
                        </option>
                      ))}
                    </select>
                    <Button
                      variant="contained"
                      onClick={handleAddMappings}
                      disabled={saving || selectedProjectIds.length === 0}
                      loading={saving}
                      style={{ alignSelf: 'flex-start' }}
                    >
                      新增映射
                    </Button>
                  </>
                )}
              </div>
            </>
          ) : (
            <p>請先選擇一個折扣</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default DiscountProjectMappingPage;

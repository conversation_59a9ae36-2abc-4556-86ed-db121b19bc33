import React from 'react';
import './OrderSummary.css';

interface CartItem {
  id: string;
  name: string;
  quantity: number;
  subtotal: number;
}

interface OrderSummaryProps {
  cartItems: CartItem[];
  subtotal: number;
  discount: number;
  total: number;
  remarks: string;
  pickupDate: string;
  onRemarksChange: (value: string) => void;
  onPickupDateChange: (value: string) => void;
  onSubmit: () => void;
  onRemoveItem: (id: string) => void;
  appliedDiscount?: any | null;
  submitDisabled?: boolean;
  message?: string;
  messageType?: 'success' | 'error' | '';
}

const OrderSummary: React.FC<OrderSummaryProps> = ({
  cartItems,
  subtotal,
  discount,
  total,
  remarks,
  pickupDate,
  onRemarksChange,
  onPickupDateChange,
  onSubmit,
  onRemoveItem,
  appliedDiscount,
  submitDisabled,
  message,
  messageType,
}) => {
  return (
    <section className="order-summary card">
      <div className="summary-section selected-items-summary">
        <h2 className="section-title">訂單內容</h2>
        <div className="cart-items">
          {cartItems.length === 0 ? (
            <p className="cart-empty-message">請從上方選擇商品</p>
          ) : (
            cartItems.map(item => (
              <div className="cart-item" key={item.id}>
                <div className="cart-item-info">
                  <div className="cart-item-name">{item.name}</div>
                  <div className="item-quantity">(x{item.quantity})</div>
                </div>
                <div className="cart-item-actions">
                  <div className="cart-item-price">NT$ {item.subtotal.toLocaleString()}</div>
                  <button
                    className="btn btn-outline btn-sm remove-item-btn"
                    onClick={() => onRemoveItem(item.id)}
                  >
                    移除
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      <div className="summary-section calculation-summary">
        <h2 className="section-title">金額計算</h2>
        {appliedDiscount ? (
          <div className="discount-info">
            <p>
              已套用優惠：<strong>{appliedDiscount.name}</strong>
            </p>
            <p>
              滿 {appliedDiscount.quantity_threshold} 件，每件折扣 NT${' '}
              {appliedDiscount.discount_per_item}
            </p>
            <p>總折扣金額：NT$ {discount.toLocaleString()}</p>
          </div>
        ) : (
          <div className="discount-info">
            <p>未達優惠門檻</p>
          </div>
        )}
        <div className="total-section">
          <div className="total-row">
            <div className="total-label">商品小計</div>
            <div className="total-amount">NT$ {subtotal.toLocaleString()}</div>
          </div>
          <div className="total-row discount-row">
            <div className="total-label">數量折扣</div>
            <div className="total-amount">- NT$ {discount.toLocaleString()}</div>
          </div>
          <div className="total-row final-total-row">
            <div className="total-label">訂單總金額</div>
            <div className="total-amount">NT$ {total.toLocaleString()}</div>
          </div>
        </div>
      </div>

      <div className="summary-section order-submission">
        <h2 className="section-title">訂單資訊</h2>
        <div className="form-group">
          <label htmlFor="remarks" className="form-label">
            備註 (選填)
          </label>
          <textarea
            id="remarks"
            className="form-textarea"
            placeholder="請填寫備註事項或特殊需求..."
            value={remarks}
            onChange={e => onRemarksChange(e.target.value)}
          />
        </div>
        <div className="form-group">
          <label htmlFor="pickupDate" className="form-label">
            預計取貨日期 (選填)
          </label>
          <input
            type="date"
            id="pickupDate"
            className="form-input"
            value={pickupDate}
            onChange={e => onPickupDateChange(e.target.value)}
          />
        </div>
        <div className="form-actions">
          <button
            className="btn btn-primary btn-lg submit-btn"
            onClick={onSubmit}
            disabled={submitDisabled}
          >
            <span className="nav-icon check-circle-icon"></span>
            確認送出訂單
          </button>
        </div>
        {message && (
          <div
            className={`submission-message ${
              messageType === 'success' ? 'success' : messageType === 'error' ? 'error' : ''
            }`}
            style={{ marginTop: '1rem', textAlign: 'right' }}
          >
            {message}
          </div>
        )}
      </div>
    </section>
  );
};

export default OrderSummary;

import React from 'react';
import styles from './styles.module.css';

/**
 * 圖標元件屬性介面
 */
interface IconProps {
  /**
   * 自定義 CSS 類名
   */
  className?: string;
}

/**
 * 首頁圖標元件
 *
 * @param props 圖標元件屬性
 * @returns 首頁圖標元件
 */
export const HomeIcon: React.FC<IconProps> = ({ className }) => (
  <svg
    className={`${styles.icon} ${className || ''}`}
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9 22V12h6v10"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

/**
 * 購物車圖標元件
 *
 * @param props 圖標元件屬性
 * @returns 購物車圖標元件
 */
export const CartIcon: React.FC<IconProps> = ({ className }) => (
  <svg
    className={`${styles.icon} ${className || ''}`}
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9 22a1 1 0 100-2 1 1 0 000 2zM20 22a1 1 0 100-2 1 1 0 000 2z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1 1h4l2.68 13.39a2 2 0 002 1.61h9.72a2 2 0 002-1.61L23 6H6"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

/**
 * 訂單圖標元件
 *
 * @param props 圖標元件屬性
 * @returns 訂單圖標元件
 */
export const OrderIcon: React.FC<IconProps> = ({ className }) => (
  <svg
    className={`${styles.icon} ${className || ''}`}
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14 2v6h6M16 13H8M16 17H8M10 9H8"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

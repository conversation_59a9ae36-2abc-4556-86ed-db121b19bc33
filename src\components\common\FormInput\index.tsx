import React, { useState, useEffect } from 'react';
import styles from './styles.module.css';

/**
 * 表單輸入元件屬性介面
 * 定義表單輸入元件的所有可配置屬性
 */
interface FormInputProps {
  /**
   * 輸入框標籤
   */
  readonly label: string;
  /**
   * 輸入框名稱
   */
  readonly name: string;
  /**
   * 輸入框類型
   * @default 'text'
   */
  readonly type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'date';
  /**
   * 輸入框值
   */
  readonly value: string;
  /**
   * 輸入框佔位符
   * @default ''
   */
  readonly placeholder?: string;
  /**
   * 輸入框是否必填
   * @default false
   */
  readonly required?: boolean;
  /**
   * 輸入框是否禁用
   * @default false
   */
  readonly disabled?: boolean;
  /**
   * 輸入框錯誤訊息
   * 當設置此屬性時，輸入框將顯示錯誤狀態
   * @default ''
   */
  readonly error?: string;
  /**
   * 輸入框幫助文字
   * 在輸入框下方顯示的輔助說明文字
   * @default ''
   */
  readonly helperText?: string;
  /**
   * 輸入框值變更回調函數
   * 當輸入框值變更時調用
   */
  readonly onChange: (value: string) => void;
  /**
   * 輸入框失焦回調函數
   * 當輸入框失去焦點時調用
   */
  readonly onBlur?: () => void;
  /**
   * 輸入框最大長度
   * 限制輸入框可輸入的最大字符數
   */
  readonly maxLength?: number;
  /**
   * 輸入框最小值（僅適用於 type="number"）
   */
  readonly min?: number;
  /**
   * 輸入框最大值（僅適用於 type="number"）
   */
  readonly max?: number;
  /**
   * 輸入框步進值（僅適用於 type="number"）
   */
  readonly step?: number;
  /**
   * 輸入框自動完成
   * 控制瀏覽器的自動完成功能
   */
  readonly autoComplete?: string;
}

/**
 * 表單輸入元件
 *
 * 用於收集用戶輸入的表單控件，支持多種輸入類型和狀態。
 * 提供標籤、錯誤提示、幫助文字等功能，並使用 CSS Module 確保樣式隔離。
 *
 * 使用示例:
 * ```tsx
 * <FormInput
 *   label="電子郵件"
 *   name="email"
 *   type="email"
 *   value={email}
 *   onChange={setEmail}
 *   required
 *   placeholder="請輸入您的電子郵件"
 *   helperText="我們不會向您發送垃圾郵件"
 * />
 * ```
 *
 * @param props - 表單輸入元件屬性
 * @returns 表單輸入元件
 */
const FormInput: React.FC<FormInputProps> = ({
  label,
  name,
  type = 'text',
  value,
  placeholder = '',
  required = false,
  disabled = false,
  error = '',
  helperText = '',
  onChange,
  onBlur,
  maxLength,
  min,
  max,
  step,
  autoComplete,
}) => {
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [internalValue, setInternalValue] = useState<string>(value);
  const inputId = `input-${name}`;
  const hasError = error !== '';

  // 當外部 value 變化時更新內部狀態
  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  /**
   * 處理輸入變更
   */
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const newValue = e.target.value;
    setInternalValue(newValue);
    onChange(newValue);
  };

  /**
   * 處理輸入聚焦
   */
  const handleFocus = (): void => {
    setIsFocused(true);
  };

  /**
   * 處理輸入失焦
   */
  const handleBlur = (): void => {
    setIsFocused(false);
    if (onBlur) {
      onBlur();
    }
  };

  const containerClassName = `${styles.container} ${hasError ? styles.hasError : ''}`;
  const inputWrapperClassName = `${styles.inputWrapper} ${isFocused ? styles.focused : ''} ${disabled ? styles.disabled : ''}`;

  return (
    <div className={containerClassName}>
      <label htmlFor={inputId} className={styles.label}>
        {label}
        {required && <span className={styles.requiredMark}>*</span>}
      </label>

      <div className={inputWrapperClassName}>
        <input
          id={inputId}
          name={name}
          type={type}
          value={internalValue}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          maxLength={maxLength}
          min={min}
          max={max}
          step={step}
          autoComplete={autoComplete}
          className={styles.input}
          aria-invalid={hasError}
          aria-describedby={
            hasError ? `${inputId}-error` : helperText ? `${inputId}-helper` : undefined
          }
        />
      </div>

      {hasError && (
        <div id={`${inputId}-error`} className={styles.errorMessage} role="alert">
          {error}
        </div>
      )}

      {!hasError && helperText && (
        <div id={`${inputId}-helper`} className={styles.helperText}>
          {helperText}
        </div>
      )}
    </div>
  );
};

export default FormInput;

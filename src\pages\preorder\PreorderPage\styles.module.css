@import '../../../styles/variables.css';

/* --- Preorder Page Specific Styles --- */

.mainContent {
  flex: 1;
  padding-top: var(--space-lg);
  padding-bottom: var(--space-lg);
  /* margin-bottom is handled by body padding in prototype, adjust if needed */
}

.container {
  max-width: 1200px; /* Match prototype container */
  margin: 0 auto;
  padding-left: var(--space-md);
  padding-right: var(--space-md);
}

/* --- Base Card Style (Applied to sections) --- */
.cardBase {
  background-color: var(--paper);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md); /* Use project's shadow */
  margin-bottom: var(--space-xl); /* Spacing between cards */
  padding: var(--space-xl); /* Consistent padding */
  transition: box-shadow 0.3s ease;
}

.cardBase:hover {
  box-shadow: var(--shadow-lg); /* Use project's shadow */
}

/* --- 1. Project Details Section Styles --- */
.projectDetails {
  composes: cardBase; /* Inherit base card styles */
  /* Add specific overrides if needed */
}

.projectTitle {
  font-size: var(--font-size-2xl); /* Use project's xxl equivalent */
  font-weight: 700;
  margin-bottom: var(--space-lg);
  color: var(--text-primary);
  line-height: 1.2;
}

.projectHighlights {
  display: flex;
  flex-wrap: nowrap; /* Keep highlights in one line if possible */
  gap: var(--space-xl);
  margin-bottom: var(--space-lg);
  overflow-x: auto; /* Allow scrolling if they overflow */
  padding-bottom: var(--space-sm); /* Space for scrollbar if needed */
}

.highlightItem {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  flex-shrink: 0; /* Prevent shrinking */
}

.highlightIcon {
  font-size: 24px; /* Match prototype */
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background); /* Use project background */
  border-radius: var(--radius-full);
}

.highlightText {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  font-weight: 500;
}

.projectMeta {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.projectStatus {
  padding: var(--space-xs) var(--space-lg);
  font-weight: 500;
  font-size: var(--font-size-base);
  border-radius: var(--radius-full);
  transition: transform 0.2s ease;
}

.projectStatus:hover {
  transform: translateY(-1px);
}

/* Map prototype status colors to project variables if possible */
/* Assuming project has similar status color variables */
.statusActive {
  background-color: var(--success-light); /* Use project success light */
  color: var(--success-dark); /* Use project success dark */
}

.statusOrderingEnded {
  background-color: var(--warning-light); /* Use project warning light */
  color: var(--warning-dark); /* Use project warning dark */
}

.statusArrived {
  background-color: var(--info-light); /* Use project info light */
  color: var(--info-dark); /* Use project info dark */
}

.statusCompleted {
  background-color: var(--background); /* Use project background */
  color: var(--text-secondary); /* Use project text secondary */
}

.projectDeadline {
  color: var(--text-secondary); /* Use project text secondary */
  font-size: var(--font-size-base);
  font-weight: 500;
  padding: var(--space-xs) var(--space-md);
  background-color: var(--background); /* Use project background */
  border-radius: var(--radius-full);
}

.projectDescription {
  color: var(--text-secondary); /* Use project text secondary */
  line-height: 1.7; /* Use relaxed line height value */
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-lg);
}

/* Gallery Styles - Keep existing gallery styles if they work */
.projectGalleryContainer {
  width: 100%;
  overflow: hidden;
  background-color: var(--background);
  position: relative;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  margin-top: 0; /* Reset margin if needed */
}

.projectGallery {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  gap: var(--space-md);
  padding: var(--space-md);
  height: 300px; /* Match prototype */
  align-items: center;
  scrollbar-width: none; /* Hide scrollbar */
  -ms-overflow-style: none; /* Hide scrollbar IE/Edge */
}

.projectGallery::-webkit-scrollbar {
  display: none; /* Hide scrollbar Chrome/Safari */
}

.galleryImage {
  height: 100%;
  width: auto; /* Let height control aspect ratio */
  max-width: 90%; /* Prevent image taking full width if very wide */
  object-fit: contain; /* Show full image */
  border-radius: var(--radius-md);
  scroll-snap-align: start;
  flex-shrink: 0;
  transition: opacity 0.3s ease;
}

/* --- 2. Items Selection Section Styles --- */
.itemsContainer {
  composes: cardBase;
}

.sectionTitle {
  font-size: var(--font-size-xl); /* Use project xl */
  font-weight: 700;
  margin-bottom: var(--space-xl);
  color: var(--text-primary);
  padding-bottom: var(--space-md);
  border-bottom: 2px solid var(--border-color); /* Use project border */
}

.itemsList {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

/* Item Card Styles (Applied within ItemCard component, adjust there if needed) */
/* These styles might be better placed in ItemCard.module.css */
/* For now, defining here based on prototype structure */
.itemCard {
  display: grid;
  grid-template-columns: 1fr; /* Mobile first */
  align-items: center;
  gap: var(--space-lg);
  background-color: var(--paper); /* Use paper for card background */
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  border: 1px solid var(--border-color); /* Use project border */
  transition: all 0.2s ease;
}

.itemCard:hover {
  border-color: var(--primary-light); /* Use project primary light */
  box-shadow: var(--shadow-sm); /* Use project shadow */
  transform: translateY(-1px);
}

@media (min-width: 600px) {
  .itemCard {
    grid-template-columns: 2fr auto auto; /* Desktop layout */
    gap: var(--space-xl);
  }
}

.itemInfo {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.itemName {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
}

.itemDescription {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  line-height: 1.7; /* Relaxed line height */
}

.itemPrice {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--secondary); /* Use project secondary */
  text-align: right;
  white-space: nowrap;
}

/* Quantity Control (Applied within ItemCard component) */
.quantityControl {
  display: flex;
  align-items: center;
  background-color: var(--background); /* Use project background */
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color); /* Use project border */
  padding: var(--space-xs);
  width: 120px; /* Match prototype */
  justify-self: end;
  transition: border-color 0.2s ease;
}

.quantityControl:hover {
  border-color: var(--primary-light); /* Use project primary light */
}

@media (max-width: 599px) {
  .quantityControl {
    justify-self: start; /* Align left on mobile */
    margin-top: var(--space-sm);
  }
}

.quantityBtn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--text-secondary); /* Use project text secondary */
  font-size: var(--font-size-lg);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.quantityBtn:hover:not(:disabled) {
  background-color: var(--border-color); /* Use project border for light hover */
  color: var(--text-primary);
}

.quantityBtn:disabled {
  color: var(--text-disabled); /* Use project disabled text */
  cursor: not-allowed;
}

.quantityInput {
  width: 48px;
  height: 32px;
  border: none;
  border-left: 1px solid var(--border-color); /* Use project border */
  border-right: 1px solid var(--border-color); /* Use project border */
  text-align: center;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  font-weight: 500;
  background: transparent;
  padding: 0 var(--space-xs);
  appearance: textfield;
  -moz-appearance: textfield;
}

.quantityInput::-webkit-outer-spin-button,
.quantityInput::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* --- 3. Order Summary & Submission Section Styles --- */
.orderSummary {
  composes: cardBase;
}

.summarySection {
  margin-bottom: var(--space-xl); /* Space between summary parts */
}

.summarySection:last-child {
  margin-bottom: 0;
}

/* Selected Items Summary */
.selectedItemsSummary .sectionTitle {
  margin-bottom: var(--space-lg); /* Keep prototype spacing */
}

.cartItems {
  margin-top: var(--space-lg); /* Keep prototype spacing */
}

.cartEmptyMessage {
  text-align: center;
  color: var(--text-disabled); /* Use project disabled text */
  padding: var(--space-xl);
  font-style: italic;
  border: 2px dashed var(--border-color); /* Use project border */
  border-radius: var(--radius-lg);
  background-color: var(--background); /* Use project background */
}

.cartItem {
  display: flex;
  flex-wrap: wrap; /* Allow wrapping on small screens */
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md) var(--space-lg); /* Match prototype padding */
  gap: var(--space-md);
  border-bottom: 1px solid var(--border-color); /* Use project border */
  transition: background-color 0.2s ease;
}

.cartItem:hover {
  background-color: var(--background); /* Use project background */
}

.cartItem:last-child {
  border-bottom: none;
}

.cartItemInfo {
  display: flex;
  flex-direction: column; /* Stack name and quantity */
  gap: var(--space-xs);
  flex-grow: 1; /* Take available space */
}

.cartItemName {
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

.itemQuantity { /* Renamed from prototype for clarity */
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.cartItemActions {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  flex-shrink: 0; /* Prevent shrinking */
}

/* Assuming Button component handles its own styles */
/* Style remove button specifically if needed */
.removeItemBtn {
  /* Use Button component props if possible, otherwise style here */
  /* Example direct styling based on prototype: */
  border: 2px solid var(--error) !important; /* Use project error color */
  color: var(--error) !important;
  background-color: transparent;
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.removeItemBtn:hover {
  background-color: var(--error-light) !important; /* Use project error light */
  transform: translateY(-1px);
}

.cartItemPrice {
  font-weight: 600;
  color: var(--text-primary); /* Match prototype */
  white-space: nowrap;
  min-width: 80px; /* Ensure space */
  text-align: right;
}

/* Calculation Summary */
.calculationSummary .sectionTitle {
  margin-bottom: var(--space-lg); /* Match prototype */
}

.discountInfo {
  margin-top: calc(-1 * var(--space-lg) + var(--space-sm)); /* Pull up slightly */
  margin-bottom: var(--space-md);
  font-size: var(--font-size-sm);
  color: var(--primary); /* Use project primary */
  text-align: right;
  font-weight: 500;
  min-height: 1.4em; /* Reserve space */
}

.totalSection {
  border-top: 2px solid var(--border-color); /* Use project border */
  padding-top: var(--space-lg);
  margin-top: 0; /* Reset margin */
  display: flex;
  flex-direction: column;
  align-items: flex-end; /* Align totals to the right */
  gap: var(--space-md);
}

.totalRow {
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 350px; /* Limit width like prototype */
  font-size: var(--font-size-base);
  padding: var(--space-xs) 0;
  transition: background-color 0.2s ease;
}

.totalRow:hover {
  background-color: var(--background); /* Use project background */
}

.totalLabel {
  color: var(--text-secondary); /* Use project text secondary */
}

.totalAmount {
  font-weight: 500;
  min-width: 100px; /* Ensure space */
  text-align: right;
  color: var(--text-primary); /* Default total color */
}

.discountRow .totalLabel,
.discountRow .totalAmount {
  color: var(--error); /* Use project error color for discount */
}

.finalTotalRow {
  font-size: var(--font-size-xl); /* Match prototype */
  font-weight: 700;
  margin-top: var(--space-md);
  border-top: 2px solid var(--border-color); /* Use project border */
  padding-top: var(--space-md);
  background-color: var(--background); /* Use project background */
  padding: var(--space-md); /* Add padding */
  border-radius: var(--radius-md);
}

.finalTotalRow .totalLabel,
.finalTotalRow .totalAmount {
  color: var(--text-primary); /* Match prototype */
}

/* Order Submission */
.orderSubmission .sectionTitle {
  margin-bottom: var(--space-lg); /* Match prototype */
}

.formGroup { /* Style form groups if needed */
  margin-bottom: var(--space-lg);
}

.formLabel { /* Style labels if needed */
  display: block;
  font-weight: 500;
  margin-bottom: var(--space-sm);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.formInput, .formTextarea { /* Style inputs/textareas if needed */
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  line-height: 1.6;
  background-color: var(--paper);
  transition: all 0.2s ease;
}

.formInput:focus, .formTextarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light); /* Adjust focus shadow */
}

.formTextarea {
  min-height: 100px;
  resize: vertical;
}

.formActions {
  display: flex;
  justify-content: flex-end; /* Align button right */
  margin-top: var(--space-xl);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--border-color); /* Use project border */
}

/* Assuming Button component handles its own styles */
/* Add specific styles for the submit button if Button component is not used or needs override */
.submitBtn {
  /* Use Button component props if possible */
  /* Example direct styling based on prototype: */
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-xl); /* Large padding */
  font-size: var(--font-size-lg); /* Large font */
  font-weight: 600;
  /* Styles below assume Button component is NOT used */
  /* background-color: var(--primary);
  color: var(--primary-contrast);
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease; */
}

.submitBtn svg { /* Style icon inside button */
  width: 24px;
  height: 24px;
}

/* Hover/disabled states likely handled by Button component */
/* .submitBtn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.submitBtn:disabled {
  background-color: var(--text-disabled);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
} */

.submissionMessage {
  font-size: var(--font-size-base);
  font-weight: 500;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  margin-top: var(--space-md);
  text-align: right; /* Align message with button */
}

.submissionMessage.success {
  color: var(--success-dark); /* Use project success dark */
  background-color: var(--success-light); /* Use project success light */
}

.submissionMessage.error {
  color: var(--error-dark); /* Use project error dark */
  background-color: var(--error-light); /* Use project error light */
}


/* Responsive Adjustments from prototype */
@media (max-width: 599px) {
  .submitBtn {
    width: 100%; /* Full width button on mobile */
    justify-content: center;
  }

  .totalRow {
    max-width: none; /* Allow totals to take full width */
  }

  .projectHighlights {
    gap: var(--space-lg); /* Adjust gap for mobile */
  }

  .highlightIcon {
    width: 32px; /* Smaller icon */
    height: 32px;
    font-size: 20px;
  }

  .highlightText {
    font-size: var(--font-size-sm); /* Smaller text */
  }

  .projectTitle {
    font-size: var(--font-size-xl); /* Smaller title */
  }

  .sectionTitle {
    font-size: var(--font-size-lg); /* Smaller section title */
  }

  .cartItem {
    padding: var(--space-md); /* Adjust padding */
  }

  .cartItemActions {
    gap: var(--space-sm); /* Reduce gap */
  }
}

/* Keep existing responsive styles if needed */
@media (min-width: 768px) {
  /* .itemsGrid {
    grid-template-columns: repeat(2, 1fr);
  } */
  /* Keep itemsList as flex column for now, matching prototype */
}

/* Add any other necessary styles from the original styles.module.css */
.loadingIndicator,
.errorMessage {
  text-align: center;
  padding: var(--space-xl);
  color: var(--text-disabled);
}

.errorMessage {
  color: var(--error-dark);
}

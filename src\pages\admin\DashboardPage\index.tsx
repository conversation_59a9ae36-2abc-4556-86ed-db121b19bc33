/**
 * 注意：
 * 此頁面已由 src/components/layout/AdminLayout/index.tsx 統一包覆側邊欄與頂部
 * 請勿在此頁面重複加入側邊欄或頂部導航，避免出現兩層導航問題
 * 僅需專注於內容區域的元件
 */

import React, { useEffect, useState } from 'react';
import './styles.module.css';

const DashboardStats: React.FC<{ totalOrders: number; totalSales: number }> = ({
  totalOrders,
  totalSales,
}) => {
  return (
    <div
      className="stats-container"
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
        gap: '1.5rem',
        marginBottom: '2rem',
      }}
    >
      <div
        className="stat-card"
        style={{
          backgroundColor: 'var(--bg-white)',
          borderRadius: 'var(--radius-lg)',
          boxShadow: 'var(--shadow-md)',
          padding: '1.5rem',
          textAlign: 'center',
        }}
      >
        <p style={{ fontSize: '0.9rem', color: '#666' }}>訂單總數</p>
        <p style={{ fontSize: '2rem', fontWeight: 700 }}>{totalOrders}</p>
      </div>
      <div
        className="stat-card"
        style={{
          backgroundColor: 'var(--bg-white)',
          borderRadius: 'var(--radius-lg)',
          boxShadow: 'var(--shadow-md)',
          padding: '1.5rem',
          textAlign: 'center',
        }}
      >
        <p style={{ fontSize: '0.9rem', color: '#666' }}>總銷售額</p>
        <p style={{ fontSize: '2rem', fontWeight: 700 }}>NT$ {totalSales}</p>
      </div>
      <div
        className="stat-card"
        style={{
          backgroundColor: 'var(--bg-white)',
          borderRadius: 'var(--radius-lg)',
          boxShadow: 'var(--shadow-md)',
          padding: '1.5rem',
          textAlign: 'center',
        }}
      >
        <p>統計區</p>
      </div>
      <div
        className="stat-card"
        style={{
          backgroundColor: 'var(--bg-white)',
          borderRadius: 'var(--radius-lg)',
          boxShadow: 'var(--shadow-md)',
          padding: '1.5rem',
          textAlign: 'center',
        }}
      >
        <p>統計區</p>
      </div>
    </div>
  );
};

const DashboardChart: React.FC = () => (
  <div
    className="chart-container"
    style={{
      backgroundColor: 'var(--bg-white)',
      borderRadius: 'var(--radius-lg)',
      boxShadow: 'var(--shadow-md)',
      padding: '1.5rem',
      marginBottom: '2rem',
      textAlign: 'center',
    }}
  >
    <p>圖表區</p>
  </div>
);

const RecentActivity: React.FC = () => (
  <div
    className="recent-activity"
    style={{
      backgroundColor: 'var(--bg-white)',
      borderRadius: 'var(--radius-lg)',
      boxShadow: 'var(--shadow-md)',
      padding: '1.5rem',
      textAlign: 'center',
    }}
  >
    <p>最近活動</p>
  </div>
);

const DashboardPage: React.FC = () => {
  const [totalOrders, setTotalOrders] = useState(0);
  const [totalSales, setTotalSales] = useState(0);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const {
          data: ordersData,
          count: totalOrdersCount,
          error: countError,
        } = await import('../../../lib/supabase').then(({ supabase }) =>
          supabase.from('orders').select('*', { count: 'exact', head: true })
        );

        if (countError) {
          console.error('取得訂單總數失敗', countError);
          setTotalOrders(0);
        } else {
          setTotalOrders(totalOrdersCount ?? 0);
        }

        const { data: salesData, error: salesError } = await import('../../../lib/supabase').then(
          ({ supabase }) =>
            supabase
              .from('orders')
              .select('final_amount, status')
              .in('status', ['Confirmed', 'Completed'])
        );

        if (salesError) {
          console.error('取得銷售額失敗', salesError);
          setTotalSales(0);
        } else {
          const totalSales =
            salesData?.reduce(
              (sum: number, o: { final_amount?: number }) => sum + (o.final_amount ?? 0),
              0
            ) ?? 0;
          setTotalSales(totalSales);
        }
      } catch (error) {
        console.error('載入統計資料失敗', error);
      }
    };
    fetchStats();
  }, []);

  return (
    <div style={{ padding: '2rem' }}>
      <h1
        className="page-title"
        style={{
          fontSize: '1.8rem',
          fontWeight: 700,
          color: 'var(--text-dark)',
          marginBottom: '2rem',
        }}
      >
        系統儀表板
      </h1>

      <DashboardStats totalOrders={totalOrders} totalSales={totalSales} />
      <DashboardChart />
      <RecentActivity />
    </div>
  );
};

export default DashboardPage;

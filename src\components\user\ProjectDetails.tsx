import React from 'react';
import './ProjectDetails.css';

interface Highlight {
  icon: string;
  text: string;
}

interface ProjectDetailsProps {
  title: string;
  description: string;
  status: string;
  deadline: string;
  highlights: Highlight[];
  images: string[];
  message?: string;
  messageType?: 'success' | 'error' | '';
}

const ProjectDetails: React.FC<ProjectDetailsProps> = ({
  title,
  description,
  status,
  deadline,
  highlights,
  images,
  message,
  messageType,
}) => {
  return (
    <section className="project-details card">
      {message && (
        <div
          className={`submission-message ${
            messageType === 'success' ? 'success' : messageType === 'error' ? 'error' : ''
          }`}
          style={{ marginBottom: '1rem', textAlign: 'right' }}
        >
          {message}
        </div>
      )}
      <h1 className="project-title">{title}</h1>
      <div className="project-highlights">
        {highlights.map((h, idx) => (
          <div className="highlight-item" key={idx}>
            <div className="highlight-icon">{h.icon}</div>
            <span className="highlight-text">{h.text}</span>
          </div>
        ))}
      </div>
      <div className="project-meta">
        <span className={`status project-status status-${status}`}>{status}</span>
        <span className="project-deadline">截止日期：{deadline}</span>
      </div>
      <p className="project-description">{description}</p>
      <div className="card-image-gallery-container project-gallery-container">
        <div className="card-image-gallery project-gallery">
          {images.length > 0 ? (
            images.map((src, idx) => (
              <img
                key={idx}
                src={src}
                alt={`${title} 圖片${idx + 1}`}
                className="gallery-image"
                loading="lazy"
              />
            ))
          ) : (
            <div className="gallery-image-placeholder">暫無圖片</div>
          )}
        </div>
      </div>
    </section>
  );
};

export default ProjectDetails;

/* ItemCard styles for user frontend */

.item-card {
  display: grid;
  grid-template-columns: 1fr;
  align-items: center;
  gap: var(--space-lg);
  background-color: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.item-card:hover {
  border-color: var(--primary-lighter);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

@media (min-width: 600px) {
  .item-card {
    grid-template-columns: 2fr auto auto;
    gap: var(--space-xl);
  }
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.item-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-dark);
  line-height: 1.4;
}

.item-description {
  color: var(--text-medium);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
}

.item-price {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--secondary-color);
  text-align: right;
  white-space: nowrap;
}

/* 數量調整 */
.quantity-control {
  display: flex;
  align-items: center;
  background-color: var(--bg-light);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  padding: var(--space-xs);
  width: 120px;
  justify-self: end;
  transition: border-color 0.2s ease;
}

.quantity-control:hover {
  border-color: var(--primary-lighter);
}

@media (max-width: 599px) {
  .quantity-control {
    justify-self: start;
    margin-top: var(--space-sm);
  }
}

.quantity-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--text-medium);
  font-size: var(--font-size-lg);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.quantity-btn:hover:not(:disabled) {
  background-color: var(--bg-medium);
  color: var(--text-dark);
}

.quantity-btn:disabled {
  color: var(--text-light);
  cursor: not-allowed;
}

.quantity-input {
  width: 48px;
  height: 32px;
  border: none;
  border-left: 1px solid var(--border-light);
  border-right: 1px solid var(--border-light);
  text-align: center;
  font-size: var(--font-size-base);
  color: var(--text-dark);
  font-weight: 500;
  background: transparent;
  padding: 0 var(--space-xs);
  appearance: textfield;
  -moz-appearance: textfield;
}

.quantity-input::-webkit-outer-spin-button,
.quantity-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

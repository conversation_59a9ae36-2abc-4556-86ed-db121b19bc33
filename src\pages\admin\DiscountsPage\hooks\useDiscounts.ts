import { useEffect, useState } from 'react';
import { supabase } from '../../../../lib/supabase';

export interface Discount {
  id: string;
  name: string;
  description: string | null;
  quantity_threshold: number;
  discount_per_item: number;
  applicable_item_ids: string[] | null;
  active: boolean;
  start_date: string | null;
  end_date: string | null;
  created_at: string;
  updated_at: string;
  project_ids: string[]; // 新增：此折扣適用的專案ID陣列
}

export function useDiscounts() {
  const [loading, setLoading] = useState(true);
  const [discounts, setDiscounts] = useState<Discount[]>([]);
  const [projectMap, setProjectMap] = useState<Record<string, string>>({});
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);

      const [discountRes, mappingRes, projectRes] = await Promise.all([
        supabase.from('discounts').select('*').order('created_at', { ascending: false }),
        supabase.from('discount_project_mappings').select('project_id, discount_id'),
        supabase.from('projects').select('id, name'),
      ]);

      const discountList: Discount[] = [];

      if (discountRes.error) {
        console.error('Fetch discounts error:', discountRes.error);
      }

      if (mappingRes.error) {
        console.error('Fetch discount_project_mappings error:', mappingRes.error);
      }

      if (projectRes.error) {
        console.error('Fetch projects error:', projectRes.error);
      }

      const mapping = mappingRes.data as { project_id: string; discount_id: string }[];
      const discountToProjects: Record<string, string[]> = {};

      for (const m of mapping ?? []) {
        if (!discountToProjects[m.discount_id]) {
          discountToProjects[m.discount_id] = [];
        }
        discountToProjects[m.discount_id].push(m.project_id);
      }

      for (const d of (discountRes.data ?? []) as any[]) {
        if (!d) continue;
        discountList.push({
          ...d,
          applicable_item_ids: d.applicable_item_ids ?? [],
          project_ids: discountToProjects[d.id] ?? [],
        });
      }

      setDiscounts(discountList);

      if (projectRes.data) {
        const map: Record<string, string> = {};
        for (const p of projectRes.data) {
          map[p.id] = p.name;
        }
        setProjectMap(map);
      }

      setLoading(false);
    }

    fetchData();
  }, []);

  return {
    loading,
    paginatedDiscounts: discounts,
    projectMap,
    currentPage,
    setCurrentPage,
    filterValues: {},
    handleFilterChange: () => {},
    handleReset: () => {},
    filteredDiscounts: discounts,
    handleEdit: async (discount: Discount) => {
      const { error } = await supabase
        .from('discounts')
        .update({
          name: discount.name,
          description: discount.description,
          quantity_threshold: discount.quantity_threshold,
          discount_per_item: discount.discount_per_item,
          applicable_item_ids: discount.applicable_item_ids,
          active: discount.active,
          start_date: discount.start_date,
          end_date: discount.end_date,
          updated_at: new Date().toISOString(),
        })
        .eq('id', discount.id);

      if (error) {
        console.error('更新折扣失敗', error);
        alert('更新失敗');
      } else {
        setDiscounts(prev => prev.map(d => (d.id === discount.id ? { ...d, ...discount } : d)));
        alert('更新成功');
      }
    },
    handleDelete: async (discount: Discount | undefined) => {
      if (!discount) {
        console.error('handleDelete called with undefined discount');
        alert('刪除失敗：資料異常');
        return;
      }
      console.log('刪除折扣', discount);
      const confirmMessage = `確定刪除折扣 ${discount.name || discount.id} 嗎？`;
      if (!window.confirm(confirmMessage)) return;
      const { error } = await supabase.from('discounts').delete().eq('id', discount.id);
      if (error) {
        console.error('刪除折扣失敗', error);
        alert('刪除失敗');
      } else {
        setDiscounts(prev => prev.filter(d => d.id !== discount.id));
        alert('刪除成功');
      }
    },
  };
}

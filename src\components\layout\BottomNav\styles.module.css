/**
 * BottomNav 元件模組化樣式
 * 使用 CSS Module 確保樣式隔離，避免樣式衝突
 */

/* 引入全局變數 */
@import '../../../styles/variables.css';

/* 底部導航容器 */
.bottomNav {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: var(--paper);
  box-shadow: 0 -1px 8px rgba(0, 0, 0, 0.04);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--bottom-nav-height);
  z-index: var(--z-index-fixed);
  width: 100%;
  max-width: 100%;
  padding: var(--space-xs) 0 calc(var(--space-xs) + env(safe-area-inset-bottom));
}

/* 導航項目 */
.navItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  text-decoration: none;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  padding: 6px var(--space-md);
  min-width: 64px;
  position: relative;
}

/* 活動導航項目 */
.navItem.active {
  color: var(--primary);
}

/* 活動狀態指示器 */
.navItem.active::after {
  content: '';
  position: absolute;
  bottom: 6px;
  left: 50%;
  transform: translateX(-50%) scale(1);
  width: 4px;
  height: 4px;
  background-color: var(--primary);
  border-radius: 50%;
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 導航項目懸停效果 */
.navItem:hover {
  color: var(--primary);
}

.navItem:hover .navIcon {
  transform: translateY(-2px);
}

/* 導航圖標 */
.navIcon {
  width: 24px;
  height: 24px;
  margin-bottom: var(--space-xs);
  transition: all 0.2s ease;
  color: currentColor;
}

.navIcon.active {
  color: var(--primary);
}

/* 導航標籤 */
.navLabel {
  font-size: var(--font-size-xs);
  font-weight: 500;
  transition: color 0.2s ease;
  white-space: nowrap;
}

/* 安全區域支持 */
@supports (padding: max(0px)) {
  .bottomNav {
    padding-bottom: max(var(--space-xs), env(safe-area-inset-bottom));
  }
}

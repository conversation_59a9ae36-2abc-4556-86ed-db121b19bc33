import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

describe('訂單折扣整合測試', () => {
  let projectId: string;
  let userId: string;
  let orderId: string;

  beforeAll(async () => {
    // 建立測試專案與用戶
    const { data: user } = await supabase
      .from('users')
      .insert({ id: crypto.randomUUID(), display_name: '測試用戶', role: 'user' })
      .select()
      .single();
    userId = user.id;

    const { data: project } = await supabase
      .from('projects')
      .insert({
        id: crypto.randomUUID(),
        project_id_display: 'PTEST',
        name: '測試專案',
        project_status: 'active',
        owner_id: userId,
      })
      .select()
      .single();
    projectId = project.id;
  });

  it('建立訂單並套用自動折扣', async () => {
    const cartItems = [
      {
        itemId: crypto.randomUUID(),
        itemName: '商品A',
        quantity: 5,
        unitPrice: 100,
        subtotal: 500,
      },
      {
        itemId: crypto.randomUUID(),
        itemName: '商品B',
        quantity: 3,
        unitPrice: 200,
        subtotal: 600,
      },
    ];

    const { data, error } = await supabase.rpc('create_order_with_items', {
      p_user_id: userId,
      p_project_id: projectId,
      p_remarks: '測試訂單',
      p_pickup_date: new Date().toISOString().substring(0, 10),
      p_cart_items: cartItems,
    });

    expect(error).toBeNull();
    expect(data).toBeTruthy();

    orderId = data as string;

    const { data: order } = await supabase.from('orders').select('*').eq('id', orderId).single();

    expect(order.total_amount).toBe(1100);
    expect(order.discount_amount).toBeGreaterThanOrEqual(0);
    expect(order.final_amount).toBeLessThanOrEqual(order.total_amount);
    expect(order.final_amount).toBe(order.total_amount - order.discount_amount);
  });

  it('管理員手動折扣後 final_amount 正確', async () => {
    const manualDiscount = 200;

    const { data, error } = await supabase.rpc('update_order_manual_discount', {
      p_order_id: orderId,
      p_manual_discount_amount: manualDiscount,
      p_admin_id: userId,
      p_admin_notes: '手動折扣測試',
    });

    expect(error).toBeNull();
    expect(data).toBeTruthy();

    const { data: order } = await supabase.from('orders').select('*').eq('id', orderId).single();

    expect(order.manual_discount_amount).toBe(manualDiscount);
    expect(order.final_amount).toBeGreaterThanOrEqual(0);
    expect(order.final_amount).toBe(order.total_amount - order.discount_amount - manualDiscount);
  });
});

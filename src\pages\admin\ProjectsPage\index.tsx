import React, { useState, useCallback, useEffect } from 'react';
import { Table, Button, Space, Tag, message, Input, Select, DatePicker, Form, Modal } from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  CopyOutlined,
  DeleteOutlined,
  FileExcelOutlined,
} from '@ant-design/icons';
import { useProjects } from './hooks/useProjects';
import {
  supabase,
  Project,
  Discount,
  handleSupabaseError,
  checkSupabaseConnection,
} from '@/lib/supabase';
import ProjectForm from './ProjectForm';
import ProjectItemsManager from './ProjectItemsManager';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import * as XLSX from 'xlsx';

const { RangePicker } = DatePicker;

const ProjectsPage: React.FC = () => {
  const navigate = useNavigate();
  const {
    projects,
    totalCount,
    isLoading,
    error,
    filters,
    setFilters,
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    sort,
    setSort,
    refresh,
  } = useProjects();

  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [isItemsManagerVisible, setIsItemsManagerVisible] = useState(false);
  const [discounts, setDiscounts] = useState<Discount[]>([]);
  const [isAdmin, setIsAdmin] = useState(false);
  const [authChecking, setAuthChecking] = useState(true);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const connectionChecked = React.useRef(false);

  const [searchForm] = Form.useForm();

  // 載入折扣資料
  const fetchDiscounts = useCallback(async () => {
    try {
      const { data, error } = await supabase.from('discounts').select('*').eq('active', true);
      if (error) throw error;
      setDiscounts(data || []);
    } catch (err) {
      handleSupabaseError(err);
    }
  }, []);

  // 檢查用戶權限
  useEffect(() => {
    const checkAuth = async () => {
      try {
        setAuthChecking(true);
        const {
          data: { session },
          error: sessionError,
        } = await supabase.auth.getSession();

        if (sessionError) throw sessionError;
        if (!session) {
          message.error('請先登入');
          navigate('/admin/login');
          return;
        }

        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('role')
          .eq('id', session.user.id)
          .single();

        if (userError) throw userError;
        if (userData?.role !== 'admin') {
          message.error('您沒有權限存取此頁面');
          navigate('/');
          return;
        }

        setIsAdmin(true);
        setCurrentUserId(session.user.id);
      } catch (err) {
        console.error('認證檢查失敗:', err);
        message.error('認證檢查失敗');
        navigate('/admin/login');
      } finally {
        setAuthChecking(false);
      }
    };

    checkAuth();
  }, [navigate]);

  // 檢查資料庫連線
  useEffect(() => {
    if (!connectionChecked.current) {
      checkSupabaseConnection().then(result => {
        if (!result.ok) {
          message.error('資料庫連線失敗');
          console.error('連線錯誤:', result.error);
        }
        connectionChecked.current = true;
      });
    }
  }, []);

  useEffect(() => {
    fetchDiscounts();
  }, [fetchDiscounts]);

  if (authChecking) return <div>檢查認證中...</div>;
  if (!isAdmin) return null;

  if (error) {
    return <div style={{ padding: 24, color: 'red' }}>讀取專案資料時發生錯誤：{error.message}</div>;
  }

  const handleCreate = async (values: Partial<Project>) => {
    try {
      const { data, error } = await supabase.from('projects').insert([values]).select().single();
      if (error) throw error;
      message.success('專案建立成功');
      setIsFormVisible(false);
      refresh();
      return data;
    } catch (err) {
      handleSupabaseError(err);
    }
  };

  const handleUpdate = async (id: string, values: Partial<Project>) => {
    try {
      const cleanValues: Record<string, any> = {};
      Object.entries(values).forEach(([k, v]) => {
        if (k === 'newDiscount') {
          // 不更新 newDiscount 欄位
          return;
        }
        if (v !== undefined) {
          if (dayjs.isDayjs(v)) {
            cleanValues[k] = v.toISOString();
          } else {
            cleanValues[k] = v;
          }
        }
      });
      const { error } = await supabase.from('projects').update(cleanValues).eq('id', id);
      if (error) throw error;
      message.success('專案更新成功');
      setIsFormVisible(false);
      refresh();
    } catch (err) {
      handleSupabaseError(err);
    }
  };

  const handleDelete = async (project: Project) => {
    Modal.confirm({
      title: '確定刪除專案？',
      content: `此操作無法復原，確定要刪除專案「${project.name}」嗎？`,
      okText: '確定刪除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await supabase.from('projects').delete().eq('id', project.id);
          message.success('專案已刪除');
          refresh();
        } catch (err) {
          handleSupabaseError(err);
        }
      },
    });
  };

  const handleCopy = async (project: Project) => {
    try {
      if (!currentUserId) {
        message.error('未登入，無法複製');
        return;
      }
      const copyData: Partial<Project> = {
        project_id_display: `${project.project_id_display}-copy`,
        name: project.name,
        description: project.description,
        project_status: project.project_status,
        default_discount_id: project.default_discount_id,
        deadline: project.deadline,
        arrival_date: project.arrival_date,
        images: project.images,
        highlights: project.highlights,
        owner_id: currentUserId,
      };
      const { data: newProject, error } = await supabase
        .from('projects')
        .insert([copyData])
        .select()
        .single();
      if (error) throw error;

      // 取得原專案商品
      const { data: items, error: itemsError } = await supabase
        .from('items')
        .select('*')
        .eq('project_id', project.id);
      if (itemsError) throw itemsError;

      if (items && items.length > 0) {
        const newItems = items.map((item: any) => ({
          project_id: newProject.id,
          name: item.name,
          price: item.price,
          description: item.description,
          image_url: item.image_url,
          status: item.status,
        }));
        const { error: insertItemsError } = await supabase.from('items').insert(newItems);
        if (insertItemsError) throw insertItemsError;
      }

      message.success('專案與商品已複製');
      refresh();
    } catch (err) {
      handleSupabaseError(err);
    }
  };

  const handleExportTemplate = async (project: Project) => {
    try {
      // 取得商品資料
      const { data: items, error } = await supabase
        .from('items')
        .select('*')
        .eq('project_id', project.id);
      if (error) throw error;

      // 專案sheet
      const projectSheetData = [
        [
          'name',
          'description',
          'status',
          'deadline',
          'arrival_date',
          'discount_name',
          'discount_threshold',
          'discount_value',
          'discount_description',
          'highlight1_icon',
          'highlight1_text',
          'highlight2_icon',
          'highlight2_text',
          'highlight3_icon',
          'highlight3_text',
        ],
        [
          project.name || '',
          project.description || '',
          project.project_status || '',
          project.deadline || '',
          project.arrival_date || '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
        ],
      ];
      const projectSheet = XLSX.utils.aoa_to_sheet(projectSheetData);

      // 商品sheet
      const itemSheetData = [
        ['name', 'price', 'description', 'image_url'],
        ...(items || []).map((item: any) => [
          item.name,
          item.price,
          item.description,
          item.image_url || '',
        ]),
      ];
      const itemSheet = XLSX.utils.aoa_to_sheet(itemSheetData);

      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, projectSheet, 'Project');
      XLSX.utils.book_append_sheet(wb, itemSheet, 'Items');

      const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([wbout], { type: 'application/octet-stream' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${project.name || 'project'}-template.xlsx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('匯出模板失敗', err);
      message.error('匯出模板失敗');
    }
  };

  const columns = [
    {
      title: '專案編號',
      dataIndex: 'project_id_display',
      key: 'project_id_display',
      width: 150,
    },
    {
      title: '名稱',
      dataIndex: 'name',
      key: 'name',
      width: 300,
    },
    {
      title: '狀態',
      dataIndex: 'project_status',
      key: 'project_status',
      width: 120,
      render: (status: string) => {
        const statusMap = {
          active: { color: 'green', text: '進行中' },
          ordering_ended: { color: 'orange', text: '結束預購' },
          arrived: { color: 'blue', text: '已到貨' },
          completed: { color: 'gray', text: '已完成' },
        };
        const { color, text } = statusMap[status as keyof typeof statusMap] || {
          color: 'default',
          text: status,
        };
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '預設折扣',
      key: 'default_discount',
      width: 200,
      render: (_: any, record: any) => {
        const discount = discounts.find(d => d.id === record.default_discount_id);
        return discount ? `${discount.name} (${discount.id.substring(0, 8)}...)` : '-';
      },
    },
    {
      title: '截止日期',
      dataIndex: 'deadline',
      key: 'deadline',
      width: 150,
      render: (date: string) => (date ? dayjs(date).format('YYYY-MM-DD') : '-'),
    },
    {
      title: '到貨日期',
      dataIndex: 'arrival_date',
      key: 'arrival_date',
      width: 150,
      render: (date: string) => (date ? dayjs(date).format('YYYY-MM-DD') : '-'),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_: any, record: Project) => (
        <Space size="middle">
          <Button
            type="link"
            onClick={() => {
              setSelectedProject(record);
              setIsFormVisible(true);
            }}
          >
            編輯
          </Button>
          <Button
            type="link"
            onClick={() => {
              setSelectedProject(record);
              setIsItemsManagerVisible(true);
            }}
          >
            商品管理
          </Button>
          <Button icon={<FileExcelOutlined />} onClick={() => handleExportTemplate(record)}>
            匯出模板
          </Button>
          <Button icon={<CopyOutlined />} onClick={() => handleCopy(record)}>
            複製
          </Button>
          <Button icon={<DeleteOutlined />} danger onClick={() => handleDelete(record)}>
            刪除
          </Button>
        </Space>
      ),
    },
  ];

  const onSearch = () => {
    const values = searchForm.getFieldsValue();
    setFilters({
      name: values.name || undefined,
      project_status: values.project_status || undefined,
      startDate: values.dateRange ? values.dateRange[0].toDate() : undefined,
      endDate: values.dateRange ? values.dateRange[1].toDate() : undefined,
    });
  };

  const onReset = () => {
    searchForm.resetFields();
    setFilters({});
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        width: '100%',
        padding: '16px 8px',
      }}
    >
      <div
        style={{
          marginBottom: 16,
          display: 'flex',
          justifyContent: 'space-between',
          flexWrap: 'wrap',
          gap: 16,
          flexShrink: 0,
        }}
      >
        <Form form={searchForm} layout="inline" style={{ flexWrap: 'wrap', gap: 8 }}>
          <Form.Item name="name">
            <Input placeholder="專案名稱" allowClear />
          </Form.Item>
          <Form.Item name="project_status">
            <Select placeholder="狀態" allowClear style={{ width: 120 }}>
              <Select.Option value="active">進行中</Select.Option>
              <Select.Option value="ordering_ended">結束預購</Select.Option>
              <Select.Option value="arrived">已到貨</Select.Option>
              <Select.Option value="completed">已完成</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item name="dateRange">
            <RangePicker />
          </Form.Item>
          <Form.Item>
            <Button icon={<SearchOutlined />} onClick={onSearch}>
              搜尋
            </Button>
          </Form.Item>
          <Form.Item>
            <Button icon={<ReloadOutlined />} onClick={onReset}>
              重置
            </Button>
          </Form.Item>
        </Form>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setSelectedProject(null);
            setIsFormVisible(true);
          }}
        >
          新增專案
        </Button>
      </div>

      <div style={{ flexGrow: 1, overflowY: 'auto' }}>
        <Table
          columns={columns}
          dataSource={projects}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: totalCount,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['5', '10', '20', '50'],
            position: ['topRight', 'bottomRight'],
            onChange: (page, size) => {
              setCurrentPage(page);
              if (size !== pageSize) {
                setPageSize(size);
              }
            },
          }}
        />
      </div>

      {isFormVisible && (
        <ProjectForm
          project={selectedProject}
          discounts={discounts}
          onSubmit={
            selectedProject ? values => handleUpdate(selectedProject.id, values) : handleCreate
          }
          onCancel={() => {
            setSelectedProject(null);
            setIsFormVisible(false);
          }}
        />
      )}

      {isItemsManagerVisible && selectedProject && (
        <ProjectItemsManager
          projectId={selectedProject.id}
          onClose={() => {
            setSelectedProject(null);
            setIsItemsManagerVisible(false);
          }}
        />
      )}
    </div>
  );
};

export default ProjectsPage;

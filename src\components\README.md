# 小森活共用元件庫

本文檔提供了小森活應用系統中所有共用元件的概述和使用指南。

## 目錄結構

```
src/components/
├── common/         # 通用元件（按鈕、卡片、表單輸入等）
├── layout/         # 佈局元件（頁眉、底部導航等）
│   ├── Header/     # 模組化頁眉元件
│   ├── BottomNav/  # 模組化底部導航元件
│   └── UserLayout  # 用戶頁面佈局元件
├── icons/          # 圖標元件
│   ├── NavIcons/   # 導航圖標元件
│   └── index.tsx   # 圖標導出文件
├── user/           # 用戶相關元件（用戶資料彈出視窗等）
├── admin/          # 管理員相關元件
└── index.ts        # 元件導出文件
```

## 元件模組化指南

為了確保樣式隔離和提高代碼可維護性，我們採用了模組化的元件結構和 CSS Module。每個模組化元件都應該遵循以下結構：

```
ComponentName/
├── index.tsx       # 元件主文件
├── styles.module.css # CSS Module 樣式文件
└── types.ts        # 類型定義（可選）
```

### CSS Module 使用指南

- 使用 CSS Module 確保樣式隔離，避免樣式衝突
- 類名使用 camelCase 命名規則
- 引用全局變數時使用 `@import` 語句
- 避免使用全局選擇器，除非絕對必要

```tsx
// 引入樣式
import styles from './styles.module.css';

// 在元件中使用
<div className={styles.container}>
  <button className={styles.button}>點擊</button>
</div>
```

## 佈局元件

### Header

頁眉元件，顯示在所有頁面頂部的導航欄。

```tsx
import { Header } from '../components';

<Header 
  showCartButton={true}
  cartItemCount={5}
  onCartClick={() => console.log('購物車點擊')}
  onUserProfileClick={() => console.log('用戶資料點擊')}
  onSearchSubmit={(searchTerm) => console.log('搜索:', searchTerm)}
/>
```

### BottomNav

底部導航元件，顯示在移動端頁面底部的導航欄。

```tsx
import { BottomNav } from '../components';
import { defaultNavItems } from '../components/layout/nav-items';

<BottomNav items={defaultNavItems} activeItemId="home" onItemClick={handleNavItemClick} />
```

### UserLayout

用戶頁面佈局元件，包含頁眉和底部導航。

```tsx
import { UserLayout } from '../components';

<UserLayout activeNavItem="home">
  <YourPageContent />
</UserLayout>
```

## 圖標元件

### NavIcons

導航圖標元件，用於底部導航欄。

```tsx
import { HomeIcon, CartIcon, OrderIcon } from '../components/icons';

<HomeIcon className="custom-class" />
```

## 用戶元件

### ProfilePopup

用戶資料彈出視窗元件，顯示用戶資料和設置選項。

```tsx
import { ProfilePopup } from '../components/user';

<ProfilePopup 
  user={currentUser} 
  isOpen={isProfileOpen} 
  onClose={handleProfileClose} 
/>
```

## 樣式系統

小森活應用使用模組化的樣式系統，包括：

1. **主題變數**：定義在 `src/styles/theme.ts` 中，用於 MUI 主題配置
2. **CSS 變數**：定義在 `src/styles/variables.css` 中，用於全局樣式變數
3. **重置樣式**：定義在 `src/styles/reset.css` 中，用於基礎樣式重置
4. **工具類**：定義在 `src/styles/utilities.css` 中，提供通用工具類
5. **全局樣式**：定義在 `src/styles/global.css` 中，只保留應用特定的全局樣式

在開發新元件時，請優先使用 CSS Module 確保樣式隔離，並遵循上述樣式系統。

## 響應式設計

所有元件都支持響應式設計，可以在不同尺寸的屏幕上正常顯示。主要的斷點如下：

- 移動端：最大寬度 768px
- 平板：最大寬度 992px
- 桌面：最小寬度 993px

## 無障礙性

所有元件都遵循 WCAG 2.1 AA 級別的無障礙性指南，包括：

- 適當的顏色對比度
- 鍵盤導航支持
- ARIA 屬性
- 螢幕閱讀器支持

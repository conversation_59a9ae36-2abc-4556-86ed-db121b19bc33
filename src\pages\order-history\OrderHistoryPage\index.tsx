import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styles from './styles.module.css';
import { fetchUserOrders } from '../../../lib/supabase';
import Header from '../../../components/user/Header';
import { useCart } from '../../../contexts/CartContext';

/**
 * 訂單項目介面
 */
interface OrderItem {
  readonly id: string;
  readonly name: string;
  readonly price: number;
  readonly quantity: number;
  readonly image: string;
}

/**
 * 訂單介面
 */
interface Order {
  readonly id: string;
  readonly order_number?: string;
  readonly date: string;
  readonly status: 'Pending' | 'Confirmed' | 'Cancelled' | 'Completed';
  readonly items: OrderItem[];
  readonly subtotal: number;
  readonly discount: number;
  readonly total: number;
  readonly project?: {
    name?: string;
    images?: string[];
  };
}

const OrderHistoryPage: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [orderNumber, setOrderNumber] = useState('');
  const [projectId, setProjectId] = useState('');
  const [status, setStatus] = useState('');
  const [expandedOrderIds, setExpandedOrderIds] = useState<string[]>([]);
  const { totalCount } = useCart();

  const fetchOrders = async () => {
    try {
      const userId = 'demo-user-id'; // TODO: 改為實際登入用戶ID
      const data = await fetchUserOrders(userId, {
        orderNumber: orderNumber.trim() || undefined,
        projectId: projectId || undefined,
        status: status || undefined,
      });
      const mapped = (data ?? []).map((order: any) => {
        const items = (order.order_items ?? []).map((oi: any) => ({
          id: oi.item_id,
          name: oi.items?.name ?? '',
          price: oi.items?.price ?? 0,
          quantity: oi.quantity,
          image: oi.items?.image_url ?? '',
        }));
        const subtotal = items.reduce((sum: number, i: any) => sum + i.price * i.quantity, 0);
        const discount = order.discount ?? 0;
        const total = subtotal - discount;
        return {
          id: order.id,
          date: order.created_at?.split('T')[0] ?? '',
          status: order.status ?? 'pending',
          items,
          subtotal,
          discount,
          total,
        };
      });
      setOrders(mapped);
    } catch (error) {
      console.error('載入訂單失敗', error);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  /**
   * 根據訂單狀態獲取對應的顯示文字
   *
   * @param status 訂單狀態
   * @returns 狀態顯示文字
   */
  const getStatusText = (status: Order['status']): string => {
    switch (status) {
      case 'Pending':
        return '待確認';
      case 'Confirmed':
        return '已確認';
      case 'Completed':
        return '已完成';
      case 'Cancelled':
        return '已取消';
      default:
        return '';
    }
  };

  /**
   * 根據訂單狀態獲取對應的 CSS 類名
   *
   * @param status 訂單狀態
   * @returns 狀態 CSS 類名
   */
  const getStatusClassName = (status: Order['status']): string => {
    const statusMap: Record<string, string> = {
      Pending: styles.statusPending,
      Confirmed: styles.statusProcessing,
      Completed: styles.statusCompleted,
      Cancelled: styles.statusCancelled,
    };

    return `${styles.orderStatus} ${statusMap[status] || ''}`;
  };

  return (
    <main className="main-content page-order-history">
      <div className={styles.container}>
        <h1 className={styles.pageTitle}>訂單歷史</h1>

        <section className={styles.filterContainer}>
          <h2 className={styles.filterTitle}>搜尋與過濾</h2>
          <form
            className={styles.filterForm}
            onSubmit={e => {
              e.preventDefault();
              fetchOrders();
            }}
          >
            <div className="form-group">
              <label htmlFor="orderId" className="form-label">
                訂單編號
              </label>
              <input
                type="text"
                id="orderId"
                className="form-input filter-input"
                placeholder="輸入訂單編號"
                value={orderNumber}
                onChange={e => setOrderNumber(e.target.value)}
              />
            </div>
            <div className="form-group">
              <label htmlFor="projectFilter" className="form-label">
                專案
              </label>
              <select
                id="projectFilter"
                className="form-select filter-select"
                value={projectId}
                onChange={e => setProjectId(e.target.value)}
              >
                <option value="">全部專案</option>
                <option value="1">有機蔬果預購</option>
                <option value="2">天然手工皂團購</option>
                <option value="3">環保餐具組預購</option>
              </select>
            </div>
            <div className="form-group">
              <label htmlFor="statusFilter" className="form-label">
                訂單狀態
              </label>
              <select
                id="statusFilter"
                className="form-select filter-select"
                value={status}
                onChange={e => setStatus(e.target.value)}
              >
                <option value="">全部狀態</option>
                <option value="Pending">待處理</option>
                <option value="Confirmed">已確認</option>
                <option value="Completed">已完成</option>
                <option value="Cancelled">已取消</option>
              </select>
            </div>
          </form>
          <div className={styles.filterActions}>
            <button className="btn btn-primary btn-sm">搜尋</button>
          </div>
        </section>

        <section className={styles.ordersContainer}>
          {orders.map(order => (
            <div key={order.id} className={styles.orderCard}>
              <div className={styles.orderHeader}>
                <div className={styles.orderIdDate}>
                  <div className={styles.orderId}>訂單編號: {order.order_number ?? order.id}</div>
                  <div className={styles.orderDate}>下單日期: {order.date}</div>
                </div>
                <div className={`status ${styles.orderStatus} ${getStatusClassName(order.status)}`}>
                  {getStatusText(order.status)}
                </div>
              </div>
              <div className={styles.orderBody}>
                <div className={styles.orderProject}>
                  {order.project?.images && order.project.images.length > 0 && (
                    <div className={styles.projectImageContainer}>
                      <img
                        src={order.project.images[0]}
                        alt={order.project.name ?? '專案圖片'}
                        className={styles.projectImage}
                      />
                    </div>
                  )}
                  <div className="project-info">
                    <div className={styles.projectName}>{order.project?.name ?? ''}</div>
                  </div>
                </div>
                <div className={styles.orderItems}>
                  {order.items.map(item => (
                    <div key={item.id} className={styles.orderItem}>
                      <div className={styles.itemNameQty}>
                        <div className="item-name">{item.name}</div>
                        <div className={styles.itemQty}>x{item.quantity}</div>
                      </div>
                      <div className={styles.itemPrice}>${item.price.toLocaleString()}</div>
                    </div>
                  ))}
                </div>
                <div className={styles.orderSummary}>
                  <div className={styles.summaryRow}>
                    <div className={styles.summaryLabel}>小計:</div>
                    <div className={styles.summaryValue}>${order.subtotal.toLocaleString()}</div>
                  </div>
                  <div className={styles.summaryRow}>
                    <div className={styles.summaryLabel}>折扣:</div>
                    <div className={styles.summaryValue}>-${order.discount.toLocaleString()}</div>
                  </div>
                  <div className={`${styles.summaryRow} ${styles.totalRow}`}>
                    <div className={styles.summaryLabel}>總計:</div>
                    <div className={styles.summaryValue}>${order.total.toLocaleString()}</div>
                  </div>
                </div>
              </div>
              <div className={styles.orderFooter}>
                <button
                  className="btn btn-outline btn-sm"
                  onClick={() => {
                    setExpandedOrderIds(prev =>
                      prev.includes(order.id)
                        ? prev.filter(id => id !== order.id)
                        : [...prev, order.id]
                    );
                  }}
                >
                  {expandedOrderIds.includes(order.id) ? '收合詳情' : '查看詳情'}
                </button>
              </div>
              {expandedOrderIds.includes(order.id) && (
                <div className="order-details-expanded" style={{ padding: '1rem' }}>
                  <h4>訂單明細</h4>
                  <ul>
                    {order.items.map(item => (
                      <li key={item.id}>
                        {item.name} x {item.quantity} - ${item.price.toLocaleString()}
                      </li>
                    ))}
                  </ul>
                  <p>小計：${order.subtotal.toLocaleString()}</p>
                  <p>折扣：-${order.discount.toLocaleString()}</p>
                  <p>總計：${order.total.toLocaleString()}</p>
                </div>
              )}
            </div>
          ))}
        </section>
      </div>
    </main>
  );
};

export default OrderHistoryPage;

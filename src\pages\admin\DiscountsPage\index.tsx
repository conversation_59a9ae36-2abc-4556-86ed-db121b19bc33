import React, { useState, useEffect } from 'react';
import { supabase } from '../../../lib/supabase';
import Table from '../../../components/admin/Table';
import Filter, { FilterItem } from '../../../components/admin/Filter';
import Button from '../../../components/common/Button';
import styles from './styles.module.css';
import commonStyles from '../../../styles/adminCommon.module.css';
import { useDiscounts, Discount } from './hooks/useDiscounts';
import { useNavigate } from 'react-router-dom';

const DiscountsPage: React.FC = () => {
  const {
    loading,
    paginatedDiscounts,
    projectMap,
    currentPage,
    setCurrentPage,
    filterValues,
    handleFilterChange,
    handleReset,
    filteredDiscounts,
    handleEdit,
    handleDelete,
  } = useDiscounts();

  const navigate = useNavigate();

  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [projectItems, setProjectItems] = useState<{ id: string; name: string }[]>([]);
  const [selectedItemIds, setSelectedItemIds] = useState<string[] | null>(null);

  useEffect(() => {
    async function fetchItems() {
      if (!selectedProjectId) {
        setProjectItems([]);
        setSelectedItemIds(null);
        return;
      }
      const { data, error } = await supabase
        .from('items')
        .select('id, name')
        .eq('project_id', selectedProjectId)
        .order('name');
      if (error) {
        console.error('載入商品失敗', error);
        setProjectItems([]);
        setSelectedItemIds(null);
      } else {
        setProjectItems(data ?? []);
        setSelectedItemIds(null);
      }
    }
    fetchItems();
  }, [selectedProjectId]);

  const emptyDiscount: Discount = {
    id: '',
    name: '',
    description: '',
    quantity_threshold: 0,
    discount_per_item: 0,
    applicable_item_ids: null,
    active: true,
    start_date: null,
    end_date: null,
    created_at: '',
    updated_at: '',
    project_ids: [],
  };

  const [discountForm, setDiscountForm] = useState<Discount>(emptyDiscount);
  const [isEditing, setIsEditing] = useState(false);
  const [showModal, setShowModal] = useState(false);

  const openCreateModal = () => {
    setDiscountForm({ ...emptyDiscount });
    setIsEditing(false);
    setShowModal(true);
  };

  const openEditModal = (discount: Discount) => {
    setDiscountForm({ ...discount });
    setIsEditing(true);
    setShowModal(true);
  };

  const handleFormChange = (field: keyof Discount, value: any) => {
    setDiscountForm(prev => ({ ...prev, [field]: value }));
  };

  const handleProjectChange = (projectId: string) => {
    setSelectedProjectId(projectId);
    setSelectedItemIds(null);
  };

  const handleItemToggle = (itemId: string) => {
    if (!projectItems.length) return;
    if (selectedItemIds === null) {
      const allIds = projectItems.map(i => i.id);
      const newSelected = allIds.filter(id => id !== itemId);
      setSelectedItemIds(newSelected.length === allIds.length ? null : newSelected);
    } else {
      let newSelected: string[];
      if (selectedItemIds.includes(itemId)) {
        newSelected = selectedItemIds.filter(id => id !== itemId);
      } else {
        newSelected = [...selectedItemIds, itemId];
      }
      const allIds = projectItems.map(i => i.id);
      if (newSelected.length === allIds.length) {
        setSelectedItemIds(null);
      } else {
        setSelectedItemIds(newSelected);
      }
    }
  };

  const handleSave = async () => {
    if (selectedItemIds === null) {
      discountForm.applicable_item_ids = null;
    } else {
      discountForm.applicable_item_ids = selectedItemIds;
    }

    if (isEditing) {
      await handleEdit(discountForm);
    } else {
      const { data, error } = await supabase
        .from('discounts')
        .insert([
          {
            name: discountForm.name,
            description: discountForm.description,
            quantity_threshold: discountForm.quantity_threshold,
            discount_per_item: discountForm.discount_per_item,
            applicable_item_ids: discountForm.applicable_item_ids,
            active: discountForm.active,
            start_date: discountForm.start_date ?? new Date().toISOString(),
            end_date: discountForm.end_date,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        ])
        .select('*')
        .single();

      if (error) {
        console.error('新增折扣失敗', error);
        alert('新增失敗');
      } else {
        alert('新增成功');
      }
    }
    setShowModal(false);
  };

  const handleCancel = () => {
    setShowModal(false);
  };

  const renderStatus = (d: Discount) => {
    let status: 'active' | 'expired' | 'disabled' = 'disabled';
    const now = new Date();
    const start = d.start_date ? new Date(d.start_date) : null;
    const end = d.end_date ? new Date(d.end_date) : null;

    if (!d.active) {
      status = 'disabled';
    } else if (end && now > end) {
      status = 'expired';
    } else if (start && now < start) {
      status = 'disabled';
    } else {
      status = 'active';
    }

    const statusText = {
      active: '使用中',
      expired: '已過期',
      disabled: '已停用',
    }[status];

    return (
      <span className={`${styles.statusBadge} ${styles[`status${status}`]}`}>{statusText}</span>
    );
  };

  const columns = [
    {
      key: 'name',
      title: '折扣名稱',
      render: (_value: any, record: Discount) => record.name || '(未命名)',
    },
    {
      key: 'quantity_threshold',
      title: '門檻 (件)',
      render: (_value: any, record: Discount) => record.quantity_threshold,
    },
    {
      key: 'discount_per_item',
      title: '每件折扣 (元)',
      render: (_value: any, record: Discount) => record.discount_per_item,
    },
    {
      key: 'applicable_scope',
      title: '適用範圍',
      render: (_value: any, record: Discount) =>
        !record || !record.applicable_item_ids || record.applicable_item_ids.length === 0
          ? '所有商品'
          : `特定 ${record.applicable_item_ids.length} 件商品`,
    },
    {
      key: 'projects',
      title: '關聯專案',
      render: (_value: any, record: Discount) =>
        !record || !record.project_ids || record.project_ids.length === 0
          ? '全域'
          : record.project_ids.map(pid => projectMap[pid] || pid).join(', '),
    },
    {
      key: 'status',
      title: '狀態',
      render: (_value: any, record: Discount) => renderStatus(record),
    },
    {
      key: 'period',
      title: '有效期間',
      render: (_value: any, record: Discount) => {
        const start = record.start_date ? new Date(record.start_date).toLocaleDateString() : '-';
        const end = record.end_date ? new Date(record.end_date).toLocaleDateString() : '無期限';
        return `${start} - ${end}`;
      },
    },
    {
      key: 'description',
      title: '描述',
      render: (_value: any, record: Discount) =>
        record.description ? (
          <span title={record.description}>
            {record.description.length > 10
              ? record.description.slice(0, 10) + '...'
              : record.description}
          </span>
        ) : (
          '-'
        ),
    },
    {
      key: 'actions',
      title: '操作',
      render: (_value: any, record: Discount) => (
        <div className={styles.actionButtons}>
          <button onClick={() => openEditModal(record)}>編輯</button>
          <button
            onClick={async () => {
              if (!window.confirm('確定要複製這個折扣嗎？')) return;
              try {
                const { data: original, error: fetchError } = await supabase
                  .from('discounts')
                  .select(
                    'name, description, quantity_threshold, discount_per_item, applicable_item_ids, active, start_date, end_date'
                  )
                  .eq('id', record.id)
                  .single();
                if (fetchError) throw fetchError;
                const newDiscount = {
                  name: original.name + ' (複製)',
                  description: original.description,
                  quantity_threshold: original.quantity_threshold,
                  discount_per_item: original.discount_per_item,
                  applicable_item_ids: null,
                  active: original.active,
                  start_date: original.start_date,
                  end_date: original.end_date,
                };
                const { error: insertError } = await supabase
                  .from('discounts')
                  .insert([newDiscount]);
                if (insertError) throw insertError;
                alert('複製成功');
                const { data: refreshed, error: refreshError } = await supabase
                  .from('discounts')
                  .select('*');
                if (refreshError) throw refreshError;
                window.location.reload();
              } catch (err) {
                console.error(err);
                alert('複製失敗');
              }
            }}
          >
            複製
          </button>
          <button onClick={() => handleDelete(record)}>刪除</button>
        </div>
      ),
    },
  ];

  const filterItems: FilterItem[] = [
    { key: 'search', label: '搜尋', type: 'input', placeholder: '搜尋折扣名稱或ID' },
    {
      key: 'status',
      label: '狀態',
      type: 'select',
      placeholder: '選擇狀態',
      options: [
        { label: '全部', value: '' },
        { label: '使用中', value: 'active' },
        { label: '已過期', value: 'expired' },
        { label: '已停用', value: 'disabled' },
      ],
    },
  ];

  return (
    <div className={commonStyles.adminPageContainer}>
      <div className={commonStyles.pageHeader}>
        <h1 className={commonStyles.pageTitle}>折扣管理</h1>
        <div style={{ display: 'flex', gap: '1rem' }}>
          <Button variant="contained" onClick={openCreateModal}>
            新增折扣
          </Button>
          <Button variant="outlined" onClick={() => navigate('/admin/discount-project-mapping')}>
            管理專案映射
          </Button>
        </div>
      </div>

      <div className={styles.filterSection}>
        <Filter
          items={filterItems}
          values={filterValues}
          onChange={handleFilterChange}
          onReset={handleReset}
          onSubmit={() => console.log('提交篩選')}
          loading={loading}
        />
      </div>

      <div className={styles.tableCard}>
        <Table
          columns={columns}
          data={paginatedDiscounts}
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: 10,
            total: filteredDiscounts.length,
            onChange: setCurrentPage,
          }}
        />
      </div>

      {showModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modalContent}>
            <h2 className={styles.modalTitle}>{isEditing ? '編輯折扣' : '新增折扣'}</h2>
            <form
              onSubmit={e => {
                e.preventDefault();
                handleSave();
              }}
              style={{ display: 'flex', flexDirection: 'column', gap: 'var(--admin-space-lg)' }}
            >
              {/* Section 1: Basic Info */}
              <div>
                <div>
                  <label>
                    名稱 <span style={{ color: '#d32f2f' }}>*</span>
                  </label>
                  <input
                    className={styles.formInput}
                    placeholder="請輸入折扣名稱"
                    value={discountForm.name ?? ''}
                    onChange={e => handleFormChange('name', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label>
                    敘述 <span style={{ color: '#888', fontWeight: 400 }}>(選填)</span>
                  </label>
                  <textarea
                    className={styles.formInput}
                    placeholder="請輸入折扣說明"
                    value={discountForm.description ?? ''}
                    onChange={e => handleFormChange('description', e.target.value)}
                    rows={3}
                  />
                </div>
                <div style={{ display: 'flex', gap: '16px' }}>
                  <div style={{ flex: 1 }}>
                    <label>
                      數量門檻 <span style={{ color: '#d32f2f' }}>*</span>
                    </label>
                    <input
                      className={styles.formInput}
                      type="number"
                      placeholder="例如：3"
                      value={discountForm.quantity_threshold || ''}
                      onChange={e => handleFormChange('quantity_threshold', Number(e.target.value))}
                      required
                      min={1}
                    />
                  </div>
                  <div style={{ flex: 1 }}>
                    <label>
                      每件折扣金額 <span style={{ color: '#d32f2f' }}>*</span>
                    </label>
                    <input
                      className={styles.formInput}
                      type="number"
                      placeholder="例如：20"
                      value={discountForm.discount_per_item || ''}
                      onChange={e => handleFormChange('discount_per_item', Number(e.target.value))}
                      required
                      min={0}
                    />
                  </div>
                </div>
              </div>

              {/* Section 2: Status & Period */}
              <div>
                <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
                  <div className={styles.checkboxContainer} style={{ flex: 1 }}>
                    <input
                      type="checkbox"
                      id="discount-active-checkbox"
                      checked={discountForm.active}
                      onChange={e => handleFormChange('active', e.target.checked)}
                    />
                    <label htmlFor="discount-active-checkbox">啟用狀態</label>
                  </div>
                  <div style={{ flex: 1 }}>
                    <label>開始日期</label>
                    <input
                      className={styles.formInput}
                      type="date"
                      value={discountForm.start_date ? discountForm.start_date.slice(0, 10) : ''}
                      onChange={e => handleFormChange('start_date', e.target.value)}
                    />
                  </div>
                  <div style={{ flex: 1 }}>
                    <label>結束日期</label>
                    <input
                      className={styles.formInput}
                      type="date"
                      value={discountForm.end_date ? discountForm.end_date.slice(0, 10) : ''}
                      onChange={e => handleFormChange('end_date', e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {/* Section 3: Applicable Project */}
              <div>
                <label>選擇專案</label>
                <select
                  className={styles.formSelect}
                  value={selectedProjectId ?? ''}
                  onChange={e => handleProjectChange(e.target.value)}
                >
                  <option value="">請選擇專案</option>
                  {Object.entries(projectMap).map(([pid, pname]) => (
                    <option key={pid} value={pid}>
                      {pname}
                    </option>
                  ))}
                </select>
              </div>

              {/* Section 4: Applicable Items */}
              {projectItems.length > 0 && (
                <div>
                  <div style={{ fontWeight: 600, marginBottom: '8px' }}>適用商品（預設全選）</div>
                  <div
                    style={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: '12px',
                      maxHeight: 120,
                      overflowY: 'auto',
                      border: '1px solid #eee',
                      borderRadius: '6px',
                      padding: '8px',
                      background: '#f8fafc',
                    }}
                  >
                    {projectItems.map((item: { id: string; name: string }) => (
                      <div key={item.id} className={styles.checkboxContainer}>
                        <input
                          id={`item-checkbox-${item.id}`}
                          type="checkbox"
                          checked={selectedItemIds === null || selectedItemIds.includes(item.id)}
                          onChange={() => handleItemToggle(item.id)}
                        />
                        <label htmlFor={`item-checkbox-${item.id}`}>{item.name}</label>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Section 5: Buttons */}
              <div className={styles.modalButtons}>
                <button type="submit" className={styles.modalButtonPrimary}>
                  儲存
                </button>
                <button
                  type="button"
                  onClick={handleCancel}
                  className={styles.modalButtonSecondary}
                >
                  取消
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default DiscountsPage;

import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import './Header.css';
import ProfilePopup from './ProfilePopup/index';

interface HeaderProps {
  cartCount?: number;
}

const Header: React.FC<HeaderProps> = ({ cartCount = 0 }) => {
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  // TODO: 替換成真實用戶資料
  const [currentUser, setCurrentUser] = useState<{
    id: string;
    nickname: string;
    avatarUrl?: string;
  }>({
    id: 'mock-user-id',
    nickname: '測試用戶',
    avatarUrl: '',
  });

  const handleUpdateNickname = async (newNickname: string) => {
    const { supabase } = await import('../../lib/supabase');
    const { error } = await supabase
      .from('users')
      .update({ community_nickname: newNickname })
      .eq('id', currentUser.id);
    if (error) {
      console.error('更新暱稱失敗', error);
      throw error;
    }
    setCurrentUser(prev => ({ ...prev, nickname: newNickname }));
  };

  const handleLogout = () => {
    localStorage.removeItem('isAdminLoggedIn');
    localStorage.removeItem('adminUserId');
    localStorage.removeItem('userId');
    window.location.href = '/';
  };

  return (
    <>
      <header className="app-header">
        <Link to="/" className="logo">
          <img src="/assets/images/forest-life-logo.png" alt="小森活 Logo" className="logo-img" />
          <span className="logo-text">小森活</span>
        </Link>
        <div className="nav-right">
          <Link to="/preorder" className="cart-btn" aria-label="購物車">
            <span className="nav-icon cart-icon"></span>
            {cartCount > 0 && <div className="cart-badge">{cartCount}</div>}
          </Link>
          <div
            className="user-control"
            aria-label="使用者中心"
            onClick={() => setIsProfileOpen(true)}
            style={{ cursor: 'pointer' }}
          >
            <div className="user-avatar">
              <span>{currentUser.nickname.charAt(0) || 'U'}</span>
            </div>
          </div>
        </div>
      </header>

      <ProfilePopup
        isOpen={isProfileOpen}
        onClose={() => setIsProfileOpen(false)}
        nickname={currentUser.nickname}
        avatarUrl={currentUser.avatarUrl}
        onUpdateNickname={handleUpdateNickname}
        onLogout={handleLogout}
      />
    </>
  );
};

export default Header;

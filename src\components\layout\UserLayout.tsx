import React from 'react';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import { Box, Container } from '@mui/material';
import Header from '../user/Header';
import BottomNav from '../user/BottomNav';
import { defaultNavItems } from './nav-items';
import { useCart } from '../../contexts/CartContext';

interface UserLayoutProps {
  children?: React.ReactNode;
  activeNavItem?: string;
}

/**
 * 前台佈局元件
 * 提供統一的頁面結構，包含頂部導航欄、內容區域和底部導航
 */
const UserLayout: React.FC<UserLayoutProps> = ({ children, activeNavItem }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { totalCount } = useCart();

  const getActiveNavItem = (): string => {
    if (activeNavItem) return activeNavItem;
    const path = location.pathname;
    if (path === '/') return 'home';
    if (path.startsWith('/preorder')) return 'preorder';
    if (path.startsWith('/order-history')) return 'orders';
    return '';
  };

  const handleCartClick = (): void => {
    navigate('/preorder');
  };

  const handleNavItemClick = (itemId: string): void => {
    switch (itemId) {
      case 'home':
        navigate('/');
        break;
      case 'preorder':
        navigate('/preorder');
        break;
      case 'orders':
        navigate('/order-history');
        break;
      default:
        break;
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        bgcolor: 'background.default',
      }}
    >
      <Header cartCount={totalCount} />
      <Box
        component="main"
        sx={{
          flex: 1,
          py: 2,
          mt: theme => `${theme.customVariables.layout.headerHeight}px`,
          mb: theme => `${theme.customVariables.layout.footerHeight}px`,
        }}
      >
        <Container maxWidth="lg">{children ?? <Outlet />}</Container>
      </Box>
      <BottomNav />
    </Box>
  );
};

export default UserLayout;

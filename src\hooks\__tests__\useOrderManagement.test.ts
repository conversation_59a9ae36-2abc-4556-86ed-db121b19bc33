import { renderHook, act } from '@testing-library/react-hooks';
import { useOrderManagement } from '../useOrderManagement';
import { OrderStatus } from '../../types/order';

jest.mock('../../lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      ilike: jest.fn().mockReturnThis(),
      or: jest.fn().mockReturnThis(),
      gte: jest.fn().mockReturnThis(),
      lte: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
      update: jest.fn().mockResolvedValue({ error: null }),
      delete: jest.fn().mockResolvedValue({ error: null }),
    })),
  },
}));

describe('useOrderManagement', () => {
  it('should initialize with loading true and empty orders', async () => {
    const { result, waitForNextUpdate } = renderHook(() => useOrderManagement());
    expect(result.current.loading).toBe(true);
    await waitForNextUpdate();
    expect(result.current.loading).toBe(false);
    expect(Array.isArray(result.current.orders)).toBe(true);
  });

  it('should update filter and reset page', () => {
    const { result } = renderHook(() => useOrderManagement());
    act(() => {
      result.current.updateFilter({ status: OrderStatus.Pending });
    });
    expect(result.current.filter.status).toBe(OrderStatus.Pending);
    expect(result.current.pagination.current).toBe(1);
  });

  it('should update page number', () => {
    const { result } = renderHook(() => useOrderManagement());
    act(() => {
      result.current.updatePage(3);
    });
    expect(result.current.pagination.current).toBe(3);
  });

  it('should call batchUpdateStatus without error', async () => {
    const { result } = renderHook(() => useOrderManagement());
    await act(async () => {
      await result.current.batchUpdateStatus(['id1', 'id2'], OrderStatus.Completed);
    });
  });

  it('should call batchDelete without error', async () => {
    const { result } = renderHook(() => useOrderManagement());
    await act(async () => {
      await result.current.batchDelete(['id1', 'id2']);
    });
  });
});

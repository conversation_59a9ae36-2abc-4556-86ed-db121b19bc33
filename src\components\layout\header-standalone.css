/* 獨立的 header 樣式文件，不受其他樣式影響 */

/* 重新定義必要的變數，避免依賴外部 */
:root {
  --header-height: 60px;
  --header-bg: #2c4f4f;
  --header-text: #ffffff;
  --header-text-light: rgba(255, 255, 255, 0.7);
  --header-element-bg: rgba(255, 255, 255, 0.1);
  --header-element-border: rgba(255, 255, 255, 0.2);
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --radius-full: 9999px;
  --z-index-fixed: 1000;
}

/* Header 容器 - 完全獨立的樣式 */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-fixed);
  height: var(--header-height);
  padding: 0 var(--space-md);
  background-color: var(--header-bg);
  color: var(--header-text);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-sm);
  
  /* 使用 flexbox 確保垂直置中 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-md);
}

/* Logo 區域 - 簡化樣式確保垂直置中 */
.app-header .logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

/* Logo 圖片 - 完全獨立的樣式，不依賴全局 img 樣式 */
.app-header .logo-img {
  width: 42px;
  height: 42px;
  margin-right: var(--space-sm);
  object-fit: contain;
  display: inline-block;
  vertical-align: middle;
}

/* Logo 文字 - 確保與圖片對齊 */
.app-header .logo-text {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--header-text);
  display: inline-block;
  vertical-align: middle;
}

/* 搜索表單 */
.app-header .search-form {
  flex: 1;
  max-width: 480px;
  position: relative;
  margin: 0 var(--space-md);
}

.app-header .search-input {
  width: 100%;
  height: 36px;
  padding: var(--space-sm) var(--space-xl);
  padding-right: var(--space-lg);
  background-color: var(--header-element-bg);
  border: 1px solid var(--header-element-border);
  border-radius: var(--radius-full);
  color: var(--header-text);
  font-size: var(--font-size-base);
}

.app-header .search-input::placeholder {
  color: var(--header-text-light);
}

.app-header .search-btn {
  position: absolute;
  right: var(--space-sm);
  top: 50%;
  transform: translateY(-50%);
  padding: var(--space-xs);
  color: var(--header-text);
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-header .search-icon {
  width: 16px;
  height: 16px;
  opacity: 0.8;
}

/* 右側導航 */
.app-header .nav-right {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  height: 100%;
}

/* 購物車按鈕 */
.app-header .cart-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 var(--space-sm);
  color: var(--header-text-light);
  background: none;
  border: none;
  cursor: pointer;
}

.app-header .nav-icon.cart-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-header .cart-badge {
  position: absolute;
  top: 12px;
  right: 4px;
  min-width: 18px;
  height: 18px;
  padding: 0 4px;
  background-color: #FFC75A;
  color: #704800;
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: bold;
  
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

/* 用戶控制 */
.app-header .user-control {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--space-xs);
}

.app-header .user-avatar {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background-color: var(--header-element-bg);
  border: 1px solid var(--header-element-border);
  color: var(--header-text);
  font-weight: 500;
  cursor: pointer;
  
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .app-header {
    padding: 0 var(--space-sm);
  }
  
  .app-header .search-form {
    margin: 0 var(--space-sm);
    max-width: 200px;
  }
  
  .app-header .logo-text {
    display: none;
  }
}

@media (max-width: 480px) {
  .app-header .search-form {
    display: none;
  }
}

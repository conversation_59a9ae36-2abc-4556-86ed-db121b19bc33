# 小森活樣式系統

本文檔提供了小森活應用系統中樣式系統的概述和使用指南。

## 樣式系統架構

小森活應用使用模組化的樣式系統，包括：

```
src/styles/
├── theme.ts         # MUI 主題配置
├── variables.css    # 全局 CSS 變數
├── reset.css        # 基礎樣式重置
├── utilities.css    # 通用工具類
└── global.css       # 應用特定的全局樣式
```

## 樣式優先級

為了確保樣式的一致性和可維護性，請按照以下優先級使用樣式：

1. **元件級 CSS Module**：優先使用元件級的 CSS Module 樣式
2. **主題變數**：使用 MUI 主題變數進行樣式設置
3. **全局 CSS 變數**：使用全局 CSS 變數進行樣式設置
4. **工具類**：使用工具類進行輔助樣式設置
5. **全局樣式**：盡量避免使用全局樣式，除非絕對必要

## CSS 變數

全局 CSS 變數定義在 `variables.css` 文件中，包括：

### 顏色變數

```css
--primary: #4caf50;
--primary-light: #80e27e;
--primary-dark: #087f23;
--secondary: #ff9800;
--secondary-light: #ffc947;
--secondary-dark: #c66900;
--error: #f44336;
--warning: #ff9800;
--info: #2196f3;
--success: #4caf50;
--text-primary: rgba(0, 0, 0, 0.87);
--text-secondary: rgba(0, 0, 0, 0.6);
--text-disabled: rgba(0, 0, 0, 0.38);
--background: #f5f5f5;
--paper: #ffffff;
```

### 間距變數

```css
--space-xs: 4px;
--space-sm: 8px;
--space-md: 16px;
--space-lg: 24px;
--space-xl: 32px;
--space-xxl: 48px;
```

### 字體變數

```css
--font-family: 'Noto Sans TC', sans-serif;
--font-size-xs: 12px;
--font-size-sm: 14px;
--font-size-md: 16px;
--font-size-lg: 18px;
--font-size-xl: 20px;
--font-size-xxl: 24px;
--font-weight-regular: 400;
--font-weight-medium: 500;
--font-weight-bold: 700;
```

### 陰影變數

```css
--shadow-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
--shadow-2: 0 3px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
--shadow-3: 0 10px 20px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
--shadow-4: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
--shadow-5: 0 19px 38px rgba(0, 0, 0, 0.3), 0 15px 12px rgba(0, 0, 0, 0.22);
```

### 圓角變數

```css
--radius-sm: 4px;
--radius-md: 8px;
--radius-lg: 16px;
--radius-xl: 24px;
--radius-circle: 50%;
```

### Z-index 變數

```css
--z-index-drawer: 1200;
--z-index-modal: 1300;
--z-index-popover: 1400;
--z-index-tooltip: 1500;
--z-index-fixed: 1100;
```

## CSS Module 使用指南

為了確保樣式隔離和提高代碼可維護性，我們採用了 CSS Module。每個元件都應該有自己的 CSS Module 文件。

### 命名規範

- 文件名：`styles.module.css`
- 類名：使用 camelCase 命名規則
- 避免使用全局選擇器，除非絕對必要

### 使用方法

```tsx
// 引入樣式
import styles from './styles.module.css';

// 在元件中使用
<div className={styles.container}>
  <button className={styles.button}>點擊</button>
</div>

// 條件類名
<div className={`${styles.item} ${isActive ? styles.active : ''}`}>
  內容
</div>
```

## 工具類使用指南

工具類定義在 `utilities.css` 文件中，用於快速應用常見的樣式。

### 常用工具類

```css
/* 顯示與隱藏 */
.hidden { display: none; }
.visible { display: block; }

/* 間距 */
.m-0 { margin: 0; }
.p-0 { padding: 0; }
.mt-1 { margin-top: var(--space-xs); }
.mb-1 { margin-bottom: var(--space-xs); }
/* ... 更多間距工具類 */

/* 文字對齊 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* 彈性佈局 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
/* ... 更多彈性佈局工具類 */
```

## 響應式設計

小森活應用使用媒體查詢來實現響應式設計。我們定義了以下斷點：

```css
/* 移動端 */
@media (max-width: 599px) {
  /* 移動端樣式 */
}

/* 平板 */
@media (min-width: 600px) and (max-width: 959px) {
  /* 平板樣式 */
}

/* 桌面 */
@media (min-width: 960px) {
  /* 桌面樣式 */
}
```

## 最佳實踐

1. **優先使用元件級 CSS Module**：確保樣式隔離，避免樣式衝突
2. **使用 CSS 變數**：保持樣式一致性和可維護性
3. **避免內聯樣式**：除非絕對必要，否則避免使用內聯樣式
4. **避免使用 !important**：使用適當的選擇器特異性來覆蓋樣式
5. **保持樣式簡潔**：避免過度嵌套和複雜的選擇器
6. **使用語義化類名**：使用描述元素用途的類名，而不是描述其外觀
7. **遵循 BEM 命名約定**：在 CSS Module 中使用 BEM 命名約定來組織類名

## 樣式重構指南

在進行樣式重構時，請遵循以下步驟：

1. **分析現有樣式**：了解現有樣式的結構和用途
2. **創建模組化元件**：將元件拆分為模組化結構
3. **創建 CSS Module**：為每個元件創建 CSS Module 文件
4. **遷移樣式**：將現有樣式遷移到 CSS Module 中
5. **更新引用**：更新所有引用該元件的地方
6. **測試**：確保重構後的元件在所有場景下都能正常工作
7. **刪除舊文件**：在確認一切正常後，刪除舊的樣式文件

## 注意事項

- 在使用 CSS Module 時，類名會被轉換為唯一的標識符，因此不需要擔心類名衝突
- 在 CSS Module 中使用 `:global` 選擇器時要小心，它會跳過 CSS Module 的作用域隔離
- 在使用第三方庫時，可能需要使用 `:global` 選擇器來覆蓋其樣式

/* 專案管理頁面樣式 - 根據原型設計修正 */
.projectsContainer {
  width: 100%;
  padding: var(--admin-space-lg);
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-xl);
}

.pageTitle {
  font-size: var(--admin-font-size-xxl);
  font-weight: 700;
  color: var(--admin-text-dark);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-md);
}

.addButton {
  padding: var(--space-sm) var(--space-md);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: var(--shadow-sm);
}

.addButton:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.addButton:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.filterContainer {
  padding: var(--space-lg);
  margin-bottom: var(--admin-space-lg);
}

.filterHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.filterTitle {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-dark);
}

.filterRow {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--space-md);
  align-items: end;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.filterLabel {
  display: block;
  font-size: 0.875rem;
  color: var(--text-medium);
  font-weight: 500;
}

.filterInput,
.filterSelect {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-white);
  color: var(--text-dark);
  font-size: 0.95rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  height: calc(1.6em + 2 * var(--space-sm) + 2px);
  line-height: 1.6;
}

.filterInput:focus,
.filterSelect:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
  outline: none;
}

.filterButton {
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-white);
  color: var(--text-dark);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  height: calc(1.6em + 2 * var(--space-sm) + 2px);
}

.filterButton:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background-color: var(--primary-light);
}

.filterButtonPrimary {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.filterButtonPrimary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  color: white;
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.filterButtonPrimary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.projectList {
  background-color: var(--bg-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.projectTable {
  width: 100%;
  border-collapse: collapse;
}

.projectTableHead {
  background-color: var(--bg-light);
  border-bottom: 1px solid var(--border-color);
}

.projectTableHeader {
  padding: var(--space-md);
  text-align: left;
  font-weight: 600;
  color: var(--text-medium);
  font-size: 0.9rem;
}

.projectTableRow {
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s;
}

.projectTableRow:hover {
  background-color: var(--primary-light);
}

.projectTableRow:last-child {
  border-bottom: none;
}

.projectTableCell {
  padding: var(--space-md);
  font-size: 0.95rem;
  color: var(--text-dark);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.projectStatus {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  line-height: var(--line-height-tight);
}

.statusActive {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.statusDraft {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.statusClosed {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

.actionButton {
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: transparent;
  color: var(--text-medium);
  cursor: pointer;
  transition: all 0.2s;
  margin-right: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.actionButton:hover {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.actionButtonDanger:hover {
  background-color: var(--danger-light);
  color: var(--danger-color);
  border-color: var(--danger-color);
}

.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: var(--space-lg);
  gap: var(--space-sm);
}

.pageButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: var(--bg-white);
  color: var(--text-dark);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.pageButton:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background-color: var(--primary-light);
}

.pageButtonActive {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.pageButtonDisabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageButtonDisabled:hover {
  border-color: var(--border-color);
  color: var(--text-dark);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .pageHeader {
    flex-direction: column;
    gap: var(--admin-space-md);
    align-items: flex-start;
  }

  .filterRow {
    grid-template-columns: 1fr;
  }
  
  .filterGroup:last-child {
    grid-column: 1 / -1;
    justify-self: end;
  }
  
  .filterGroup:last-child .filterButton {
    width: auto;
  }
  
  .projectTableHead {
    display: none;
  }
  
  .projectTableRow {
    display: block;
    padding: var(--space-md);
    position: relative;
    margin-bottom: var(--space-sm);
    border-bottom: 1px solid var(--border-color);
  }
  
  .projectTableCell {
    display: flex;
    padding: 0.5rem 0;
    border-bottom: 1px dashed var(--border-color);
    white-space: normal;
  }
  
  .projectTableCell:last-child {
    border-bottom: none;
  }
  
  .projectTableCell::before {
    content: attr(data-label);
    width: 40%;
    font-weight: 600;
    color: var(--text-medium);
  }
  
  .pagination {
    justify-content: center;
  }
}

/* 價格顯示相關樣式 */
.priceContainer {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.originalPrice {
  color: var(--admin-text-light);
  text-decoration: line-through;
  font-size: var(--admin-font-sm);
}

.discountedPrice {
  color: var(--admin-primary);
  font-weight: var(--admin-font-bold);
}

/* 狀態標籤樣式 */
.statusBadge {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-font-sm);
  font-weight: var(--admin-font-medium);
}

.statusdraft {
  background-color: var(--admin-status-draft-bg);
  color: var(--admin-status-draft-text);
}

.statuspublished {
  background-color: var(--admin-status-published-bg);
  color: var(--admin-status-published-text);
}

.statusarchived {
  background-color: var(--admin-status-archived-bg);
  color: var(--admin-status-archived-text);
}

/* 操作按鈕容器 */
.actionButtons {
  display: flex;
  gap: var(--admin-space-sm);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .projectsContainer {
    padding: var(--admin-space-md);
  }

  .priceContainer {
    gap: var(--admin-space-xxs);
  }
  
  .actionButtons {
    flex-direction: column;
    gap: var(--admin-space-xs);
  }
  
  .statusBadge {
    padding: var(--admin-space-xxs) var(--admin-space-xs);
  }
}

@media (max-width: 576px) {
  .projectsContainer {
    padding: var(--admin-space-sm);
  }
}

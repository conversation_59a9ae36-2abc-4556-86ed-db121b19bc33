import React, { useEffect, useState } from 'react';
import { Timeline, Card, Typography, Tag } from 'antd';
import { supabase } from '../../lib/supabaseClient';
import { formatDateTime } from '../../utils/dateUtils';

const { Text } = Typography;

interface DiscountHistoryEntry {
  id: string;
  discountId: string;
  version: number;
  changedFields: {
    [key: string]: {
      old: any;
      new: any;
    };
  };
  changedBy: string;
  changedAt: string;
  reason: string | null;
}

interface DiscountHistoryProps {
  discountId: string;
}

export const DiscountHistory: React.FC<DiscountHistoryProps> = ({ discountId }) => {
  const [history, setHistory] = useState<DiscountHistoryEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        const { data, error } = await supabase
          .from('discount_history')
          .select(
            `
            *,
            changed_by:changed_by(email)
          `
          )
          .eq('discount_id', discountId)
          .order('changed_at', { ascending: false });

        if (error) throw error;

        setHistory(data || []);
      } catch (error) {
        console.error('Error fetching discount history:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchHistory();
  }, [discountId]);

  const renderFieldChange = (field: string, change: { old: any; new: any }) => {
    const getDisplayValue = (value: any) => {
      if (value === null) return '無';
      if (Array.isArray(value)) return value.join(', ') || '無';
      if (typeof value === 'boolean') return value ? '是' : '否';
      return value.toString();
    };

    return (
      <div key={field}>
        <Text strong>{field}：</Text>
        <Text delete>{getDisplayValue(change.old)}</Text>
        <Text> → </Text>
        <Text type="success">{getDisplayValue(change.new)}</Text>
      </div>
    );
  };

  if (loading) {
    return <Card loading />;
  }

  return (
    <Timeline>
      {history.map(entry => (
        <Timeline.Item key={entry.id}>
          <Card size="small" title={`版本 ${entry.version}`}>
            <div>
              <Text type="secondary">{formatDateTime(entry.changedAt)}</Text>
              <Tag color="blue" style={{ marginLeft: 8 }}>
                {entry.changedBy}
              </Tag>
            </div>
            <div style={{ marginTop: 8 }}>
              {Object.entries(entry.changedFields).map(([field, change]) =>
                renderFieldChange(field, change)
              )}
            </div>
            {entry.reason && (
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">原因：{entry.reason}</Text>
              </div>
            )}
          </Card>
        </Timeline.Item>
      ))}
      {history.length === 0 && <Text type="secondary">尚無變更記錄</Text>}
    </Timeline>
  );
};

@import '../../../styles/admin-variables.css';

.adminLogin {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: var(--background-color);
}

.loginContainer {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
  background: #fff;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.logoImg {
  width: 40px;
  height: 40px;
  margin-right: 0.5rem;
}

.logoText {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
}

h1 {
  margin: 0.5rem 0;
  font-size: 1.25rem;
  color: var(--text-color);
}

.subtitle {
  font-size: 0.9rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.formGroup {
  width: 100%;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
}

.formGroup label {
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.formGroup input {
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: var(--border-radius);
  font-size: 1rem;
}

.loginBtn {
  width: 100%;
  padding: 0.75rem;
  background: var(--primary-color);
  color: #fff;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  transition: background 0.2s;
}

.loginBtn:hover {
  background: #1a252f;
}

.note {
  font-size: 0.8rem;
  color: #888;
  margin-top: 1rem;
  text-align: center;
}

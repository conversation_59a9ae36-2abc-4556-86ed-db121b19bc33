.tableWrapper {
  width: 100%;
  overflow-x: auto;
  background-color: var(--admin-bg-primary);
  border-radius: var(--admin-radius-md);
  box-shadow: var(--admin-shadow-sm);
}

.table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
}

.table th,
.table td {
  padding: var(--admin-space-md);
  border-bottom: 1px solid var(--admin-border-color);
}

.table th {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-secondary);
  background-color: var(--admin-bg-secondary);
}

.table tr:last-child td {
  border-bottom: none;
}

.table tr:hover {
  background-color: var(--admin-bg-secondary);
}

.user {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.userName {
  font-weight: var(--admin-font-medium);
  color: var(--admin-text-primary);
}

.userEmail {
  font-size: var(--admin-font-sm);
  color: var(--admin-text-secondary);
}

.actions {
  display: flex;
  gap: var(--admin-space-sm);
}

.actionButton {
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-radius-sm);
  background-color: transparent;
  color: var(--admin-text-secondary);
  font-size: var(--admin-font-sm);
  cursor: pointer;
  transition: var(--admin-transition-fast);
}

.actionButton:hover {
  background-color: var(--admin-primary-lighter);
  border-color: var(--admin-primary);
  color: var(--admin-primary);
}

.loading,
.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-xl);
  background-color: var(--admin-bg-primary);
  border-radius: var(--admin-radius-md);
  color: var(--admin-text-secondary);
  font-size: var(--admin-font-lg);
} 

.statusBadge {
  display: inline-block;
  padding: 0.25em 0.75em;
  border-radius: 999px;
  font-size: var(--admin-font-xs);
  font-weight: var(--admin-font-semibold);
  text-align: center;
  white-space: nowrap;
}

.status-pending {
  background-color: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
}

.status-processing {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.status-completed {
  background-color: rgba(87, 172, 90, 0.1);
  color: #57ac5a;
}

.status-cancelled {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.subInfo {
  font-size: var(--admin-font-xs);
  color: var(--admin-text-secondary);
}

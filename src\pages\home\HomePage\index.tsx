import React, { useState, useEffect } from 'react';
import Header from '../../../components/user/Header';
import SearchBar from '../../../components/user/SearchBar';
import ProjectCard, { ProjectCardProps } from '../../../components/user/ProjectCard';
import BottomNav from '../../../components/user/BottomNav';
import { fetchActiveProjects } from '../../../lib/supabase';
import { useCart } from '../../../contexts/CartContext';
import '../../../styles/nav-icons.css';
import styles from './styles.module.css';

const HomePage: React.FC = () => {
  const [projects, setProjects] = useState<ProjectCardProps[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const { totalCount } = useCart();

  useEffect(() => {
    const loadProjects = async () => {
      try {
        const data = await fetchActiveProjects();
        const projectCards: ProjectCardProps[] = data.map(p => ({
          id: p.id,
          title: p.name ?? '',
          description: p.description ?? '',
          price: p.minPrice ?? 0,
          status: p.project_status as ProjectCardProps['status'],
          highlights: (p.highlights as any) ?? [],
          images: p.images ?? [],
        }));
        setProjects(projectCards);
      } catch (error) {
        console.error('載入專案失敗', error);
      }
    };
    loadProjects();
  }, []);

  // 根據搜索詞過濾專案
  const filteredProjects = projects.filter(project =>
    project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  const handleFilterClick = () => {
    // 未來可以實現更複雜的過濾功能
    console.log('Filter clicked');
  };

  return (
    <main className="main-content page-home">
      {/* 搜索欄 - 根據原型添加 */}
      <div className="search-container">
        <SearchBar
          value={searchTerm}
          onChange={handleSearchChange}
          onFilterClick={handleFilterClick}
        />
      </div>

      <div className="container" style={{ paddingTop: 'var(--search-bar-height)' }}>
          {filteredProjects.length > 0 ? (
            filteredProjects.map(project => (
              <ProjectCard
                key={project.id}
                {...project}
                onClick={() => window.location.href = `/preorder?id=${project.id}`}
              />
            ))
          ) : (
            <div className="no-results">
              <p>沒有找到符合條件的專案</p>
            </div>
          )}
          <div className="load-more">
            <span>沒有更多專案了</span>
          </div>
        </div>
    </main>
  );
};

export default HomePage;

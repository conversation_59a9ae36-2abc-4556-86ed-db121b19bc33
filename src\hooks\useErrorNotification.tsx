import { useState, useCallback } from 'react';
import { Snackbar, Alert } from '@mui/material';

/**
 * 錯誤通知 hook
 * 提供 showError 方法，顯示錯誤訊息
 */
export function useErrorNotification() {
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState('');

  const showError = useCallback((msg: string) => {
    setMessage(msg);
    setOpen(true);
  }, []);

  const handleClose = useCallback(() => {
    setOpen(false);
  }, []);

  const ErrorSnackbar = () => (
    <Snackbar
      open={open}
      autoHideDuration={4000}
      onClose={handleClose}
      anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
    >
      <Alert onClose={handleClose} severity="error" sx={{ width: '100%' }}>
        {message}
      </Alert>
    </Snackbar>
  );

  return { showError, ErrorSnackbar };
}

export default useErrorNotification;

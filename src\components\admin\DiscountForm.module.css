/* 小森活專案主題色 antd 表單覆蓋樣式 */
.formRoot {
  background: var(--paper);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--space-xl);
  max-width: 520px;
  margin: 0 auto;
  border: 1px solid var(--border-color);
  font-family: var(--font-family);
}

:global(.ant-form-item-label > label) {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  letter-spacing: 0.5px;
}

:global(.ant-input),
:global(.ant-input-number),
:global(.ant-picker),
:global(.ant-select-selector) {
  border-radius: var(--radius-md) !important;
  background: var(--background) !important;
  color: var(--text-primary) !important;
  font-size: var(--font-size-base) !important;
  border: 1.5px solid var(--border-color) !important;
  transition: var(--transition-normal);
  box-shadow: none !important;
}

:global(.ant-input:focus),
:global(.ant-input-number:focus),
:global(.ant-picker-focused),
:global(.ant-select-focused .ant-select-selector) {
  border-color: var(--primary) !important;
  box-shadow: 0 0 0 2px var(--primary-light) !important;
  background: #f0f7ff !important;
}

:global(.ant-input::placeholder),
:global(.ant-input-number-input::placeholder),
:global(.ant-select-selection-placeholder),
:global(.ant-picker-input > input::placeholder) {
  color: var(--text-secondary) !important;
  opacity: 1;
}

:global(.ant-switch) {
  background: var(--border-color) !important;
  border-radius: var(--radius-full) !important;
}

:global(.ant-switch-checked) {
  background: var(--primary) !important;
}

:global(.ant-btn-primary) {
  background: var(--primary) !important;
  color: var(--primary-contrast) !important;
  border-radius: var(--radius-md) !important;
  font-weight: var(--font-weight-bold) !important;
  font-size: var(--font-size-base) !important;
  box-shadow: var(--shadow-sm) !important;
  border: none !important;
  transition: var(--transition-normal);
}

:global(.ant-btn-primary:hover),
:global(.ant-btn-primary:focus) {
  background: var(--primary-light) !important;
}

:global(.ant-btn) {
  border-radius: var(--radius-md) !important;
  font-size: var(--font-size-base) !important;
  font-weight: var(--font-weight-medium) !important;
  letter-spacing: 1px;
}

:global(.ant-form-item) {
  margin-bottom: var(--space-lg) !important;
}

:global(.ant-select-selector) {
  min-height: 44px !important;
}

:global(.ant-picker) {
  width: 100% !important;
  min-height: 44px !important;
}

:global(.ant-input-number) {
  width: 100% !important;
  min-height: 44px !important;
}

:global(.ant-form-item-explain-error) {
  color: var(--error) !important;
  font-size: var(--font-size-sm) !important;
}

@media (max-width: 768px) {
  .formRoot {
    padding: var(--space-md);
    max-width: 98vw;
  }
}

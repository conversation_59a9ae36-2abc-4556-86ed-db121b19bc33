import { Database } from './supabase';

export type Tables = Database['public']['Tables'];

// 用戶相關型別
export type User = Tables['users']['Row'];
export type UserRole = User['role'];

// 專案相關型別
export type Project = Tables['projects']['Row'];
export type ProjectStatus = 'active' | 'ordering_ended' | 'arrived' | 'completed';

export interface ProjectHighlight {
  icon: string;
  text: string;
}

// 商品相關型別
export type Item = Tables['items']['Row'];
export type ItemStatus = Item['status'];

// 折扣相關型別
export type Discount = Tables['discounts']['Row'];
export type DiscountType = Discount['type'];

// 訂單相關型別
export type Order = Tables['orders']['Row'];
export type OrderStatus = Order['status'];
export type OrderItem = Tables['order_items']['Row'];

// 購物車相關型別
export interface CartItem {
  item_id: string;
  item_name: string;
  quantity: number;
  unit_price: number;
  subtotal: number;
}

export interface Cart {
  items: CartItem[];
  project_id: string;
  project_name: string;
  total_amount: number;
  discount_amount: number;
  applied_discount_id: string | null;
  final_amount: number;
}

// 折扣計算相關型別
export interface DiscountCalculationResult {
  originalTotal: number;
  discountAmount: number;
  finalTotalBeforeManualDiscount: number;
  appliedDiscount: Discount | null;
}

// API 響應型別
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

import { useRef, useState } from 'react';
import { useCloudinaryUpload } from '../../hooks/useCloudinaryUpload';

interface ImageUploaderProps {
  onUploadSuccess?: (result: { url: string; publicId: string }) => void;
  onUploadError?: (error: string) => void;
  maxSizeMB?: number;
  acceptedFileTypes?: string[];
}

export const ImageUploader = ({
  onUploadSuccess,
  onUploadError,
  maxSizeMB = 5,
  acceptedFileTypes = ['image/jpeg', 'image/png', 'image/webp'],
}: ImageUploaderProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [preview, setPreview] = useState<string | null>(null);

  const { uploadFile, isUploading, progress, error } = useCloudinaryUpload({
    maxSizeMB,
    acceptedFileTypes,
  });

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 創建預覽
    const objectUrl = URL.createObjectURL(file);
    setPreview(objectUrl);

    try {
      const result = await uploadFile(file);
      onUploadSuccess?.(result);
    } catch (err) {
      onUploadError?.(err instanceof Error ? err.message : '上傳失敗');
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="image-uploader">
      <div
        className="upload-area"
        onClick={handleClick}
        style={{
          border: '2px dashed #ccc',
          borderRadius: '8px',
          padding: '20px',
          textAlign: 'center',
          cursor: 'pointer',
          position: 'relative',
          minHeight: '200px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {preview ? (
          <img
            src={preview}
            alt="預覽"
            style={{
              maxWidth: '100%',
              maxHeight: '200px',
              objectFit: 'contain',
            }}
          />
        ) : (
          <div>
            <p>點擊或拖曳圖片至此處上傳</p>
            <p style={{ fontSize: '0.8em', color: '#666' }}>
              支援的格式: {acceptedFileTypes.join(', ')}
            </p>
            <p style={{ fontSize: '0.8em', color: '#666' }}>最大檔案大小: {maxSizeMB}MB</p>
          </div>
        )}

        {isUploading && (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <p>上傳中... {progress}%</p>
          </div>
        )}
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedFileTypes.join(',')}
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />

      {error && <p style={{ color: 'red', marginTop: '10px' }}>{error}</p>}
    </div>
  );
};

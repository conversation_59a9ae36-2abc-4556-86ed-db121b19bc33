{"name": "small-forest-life", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx}\"", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@cloudinary/react": "^1.14.1", "@cloudinary/url-gen": "^1.21.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.1", "@mui/material": "^7.0.1", "@supabase/supabase-js": "^2.49.4", "antd": "^5.24.6", "date-fns": "^4.1.0", "papaparse": "^5.5.2", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-router-dom": "^7.4.1", "xlsx": "^0.18.5"}, "devDependencies": {"@modelcontextprotocol/server-postgres": "^0.6.2", "@types/jest": "^29.5.14", "@types/node": "^22.14.0", "@types/react": "^18.2.64", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.2.5", "supabase": "^2.20.5", "typescript": "^5.4.2", "typescript-plugin-css-modules": "^5.1.0", "vite": "^5.1.5", "vite-tsconfig-paths": "^5.1.4"}}
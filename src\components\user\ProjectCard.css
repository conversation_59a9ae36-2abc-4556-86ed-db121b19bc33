/* ProjectCard styles for user frontend */

.page-home .project-card,
.page-preorder .project-card {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: var(--space-xl);
  border-radius: var(--radius-lg);
  background-color: var(--bg-white);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  margin: 0;
}

.page-home .project-card:hover,
.page-preorder .project-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.page-home .project-card .card-content,
.page-preorder .project-card .card-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.page-home .project-card .card-header,
.page-preorder .project-card .card-header {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.page-home .project-card .card-title,
.page-preorder .project-card .card-title {
  font-size: var(--font-size-xxl);
  font-weight: 600;
  color: var(--text-dark);
  line-height: 1.3;
}

.page-home .project-highlights,
.page-preorder .project-highlights {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-lg);
}

.page-home .highlight-item,
.page-preorder .highlight-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.page-home .highlight-icon,
.page-preorder .highlight-icon {
  font-size: 1.5rem;
  width: 32px;
  height: 32px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-light);
  border-radius: var(--radius-full);
}

.page-home .highlight-text,
.page-preorder .highlight-text {
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: 500;
}

.page-home .project-card .card-description,
.page-preorder .project-card .card-description {
  color: var(--text-medium);
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  margin: var(--space-md) 0;
}

.page-home .project-card .card-image-gallery-container,
.page-preorder .project-gallery-container {
  width: 100%;
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--bg-light);
}

.page-home .project-card .card-image-gallery,
.page-preorder .project-gallery {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  gap: 0;
  height: 100%;
  padding: 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
  align-items: center;
}

.page-home .project-card .card-image-gallery::-webkit-scrollbar,
.page-preorder .project-gallery::-webkit-scrollbar {
  display: none;
}

.page-home .project-card .gallery-image,
.page-preorder .project-gallery .gallery-image {
  height: 100%;
  width: 100%;
  object-fit: cover;
  scroll-snap-align: start;
  flex-shrink: 0;
  border-radius: var(--radius-md);
}

.page-home .project-card .gallery-image.hidden,
.page-preorder .project-gallery .gallery-image.hidden {
  display: none;
}

.page-home .project-card .card-footer,
.page-preorder .project-card .card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--border-light);
}

.page-home .project-card .project-price,
.page-preorder .project-card .project-price {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--secondary-color);
}

.page-home .project-card .btn-primary,
.page-preorder .project-card .btn-primary {
  padding: var(--space-sm) var(--space-xl);
  font-size: var(--font-size-base);
  font-weight: 500;
}

.page-home .project-status,
.page-preorder .project-status {
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-full);
  font-size: var(--font-size-base);
  font-weight: 500;
  z-index: 1;
}

.page-home .status-active,
.page-preorder .status-active {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.page-home .status-ordering_ended,
.page-preorder .status-ordering_ended {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.page-home .status-arrived,
.page-preorder .status-arrived {
  background-color: var(--success-light);
  color: var(--success-color);
}

.page-home .status-completed,
.page-preorder .status-completed {
  background-color: var(--text-light);
  color: var(--text-medium);
}

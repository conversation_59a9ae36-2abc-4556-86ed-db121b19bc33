@import '../../../styles/admin/common.module.css';

/* 訂單管理頁面樣式 - 根據原型設計修正 */
.ordersContainer {
  width: 100%;
  padding: 24px;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-spacing-6);
}

.pageHeader h1 {
  font-size: var(--admin-font-size-2xl);
  font-weight: var(--admin-font-weight-bold);
  color: var(--admin-color-text);
  line-height: var(--admin-line-height-tight);
}

.headerActions {
  display: flex;
  gap: var(--space-md);
}

.searchInput {
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  width: 240px;
  font-size: 0.95rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.searchInput:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
  outline: none;
}

.filterSection {
  margin-bottom: var(--admin-spacing-6);
  padding: var(--admin-spacing-4);
  background-color: var(--admin-color-white);
  border: 1px solid var(--admin-color-border);
  border-radius: var(--admin-border-radius-lg);
  box-shadow: var(--admin-shadow-sm);
}

.filterGroup {
  display: flex;
  gap: var(--admin-spacing-4);
  align-items: center;
  flex-wrap: wrap;
}

.filterSelect,
.filterInput {
  flex: 1;
  min-width: 200px;
  max-width: 300px;
  height: 2.5rem;
  padding: var(--admin-spacing-2) var(--admin-spacing-3);
  border: 1px solid var(--admin-color-border);
  border-radius: var(--admin-border-radius-md);
  background-color: var(--admin-color-white);
  color: var(--admin-color-text);
  font-size: var(--admin-font-size-sm);
  transition: all 0.2s ease-in-out;
}

.filterSelect:hover,
.filterInput:hover {
  border-color: var(--admin-color-border-dark);
}

.filterSelect:focus,
.filterInput:focus {
  outline: none;
  border-color: var(--admin-color-primary);
  box-shadow: 0 0 0 2px var(--admin-color-primary-lighter);
}

.orderList {
  background-color: var(--bg-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  width: 100%;
}

.orderTable {
  width: 100%;
  border-collapse: collapse;
}

.orderTableHeader {
  padding: var(--space-md);
  text-align: left;
  font-weight: 600;
  color: var(--text-medium);
  font-size: 0.9rem;
  background-color: var(--bg-light);
  border-bottom: 1px solid var(--border-color);
}

.orderTableCell {
  padding: var(--space-md);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.95rem;
  color: var(--text-dark);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.orderTableRow {
  transition: background-color 0.2s;
}

.orderTableRow:hover {
  background-color: var(--primary-light);
}

.orderTableRow:last-child .orderTableCell {
  border-bottom: none;
}

.statusBadge {
  composes: badge;
}

.statusPending {
  background-color: #fff3e0;
  color: #e65100;
}

.statusProcessing {
  background-color: #e3f2fd;
  color: #1565c0;
}

.statusCompleted {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.statusCancelled {
  background-color: #fbe9e7;
  color: #d32f2f;
}

.actionButtons {
  display: flex;
  gap: var(--spacing-xs);
}

.actionButton {
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: transparent;
  color: var(--text-medium);
  cursor: pointer;
  transition: all 0.2s;
  margin-right: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.viewButton:hover {
  background-color: var(--info-light);
  color: var(--info-color);
  border-color: var(--info-color);
}

.editButton:hover {
  background-color: var(--warning-light);
  color: var(--warning-color);
  border-color: var(--warning-color);
}

.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: var(--space-lg);
  gap: var(--space-sm);
}

.pageButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: var(--bg-white);
  color: var(--text-dark);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.pageButton:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background-color: var(--primary-light);
}

.pageButtonActive {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.pageButtonDisabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageButtonDisabled:hover {
  border-color: var(--border-color);
  color: var(--text-dark);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .headerActions {
    flex-direction: column;
    gap: var(--space-sm);
  }
  
  .searchInput,
  .filterSelect {
    width: 100%;
  }
  
  .orderTableHeader {
    display: none;
  }
  
  .orderTableRow {
    display: block;
    padding: var(--space-md);
    position: relative;
    margin-bottom: var(--space-sm);
    border-bottom: 1px solid var(--border-color);
  }
  
  .orderTableCell {
    display: flex;
    padding: 0.5rem 0;
    border-bottom: 1px dashed var(--border-color);
    white-space: normal;
  }
  
  .orderTableCell:last-child {
    border-bottom: none;
  }
  
  .orderTableCell::before {
    content: attr(data-label);
    width: 40%;
    font-weight: 600;
    color: var(--text-medium);
  }
  
  .pagination {
    justify-content: center;
  }
  
  .actionButtons {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .actionButtons button {
    width: 100%;
  }
}

@media (max-width: 640px) {
  .filterGroup {
    flex-direction: column;
    align-items: stretch;
  }

  .filterSelect,
  .filterInput {
    max-width: none;
  }

  .actionButtons {
    flex-direction: column;
    width: 100%;
  }

  .actionButtons button {
    width: 100%;
  }
}

@import '../../../styles/variables.css';

.card {
  background-color: var(--bg-white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.imageContainer {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 寬高比 */
  overflow: hidden;
}

.image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.hover:hover .image {
  transform: scale(1.05);
}

.status {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  z-index: 1;
}

.statusPending {
  background-color: rgba(243, 156, 18, 0.1);
  color: var(--warning-color);
}

.statusActive {
  background-color: rgba(46, 204, 113, 0.1);
  color: var(--success-color);
}

.statusCompleted {
  background-color: rgba(52, 152, 219, 0.1);
  color: var(--info-color);
}

.content {
  padding: var(--space-lg);
}

.header {
  margin-bottom: var(--space-md);
  position: relative;
}

.title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--space-xs);
  color: var(--text-dark);
}

.subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-medium);
}

.body {
  margin-bottom: var(--space-md);
  color: var(--text-medium);
  font-size: var(--font-size-md);
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-sm);
  padding-top: var(--space-md);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .content {
    padding: var(--space-md);
  }
}

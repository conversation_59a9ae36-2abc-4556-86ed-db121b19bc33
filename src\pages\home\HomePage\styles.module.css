@import '../../../styles/variables.css';

/* 頁面容器 */
.pageHome {
  min-height: 100vh;
  background-color: var(--bg-light);
  padding-bottom: calc(var(--bottom-nav-height) + var(--space-md));
  width: 100vw;
  max-width: 100vw;
  overflow-x: hidden;
  margin: 0;
  padding-left: 0;
  padding-right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.mainContent {
  padding-top: calc(var(--app-header-height) + var(--search-bar-height));
  padding-bottom: calc(var(--bottom-nav-height) + var(--space-xl));
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding-left: var(--space-md);
  padding-right: var(--space-md);
}

.container {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

/* 搜索欄 */
.searchContainer {
  position: fixed;
  top: var(--app-header-height);
  left: 0;
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  background: var(--bg-white);
  display: flex;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  z-index: 999;
  height: 58px; /* 使用具體數值，避免循環引用 */
  border-bottom: 1px solid var(--border-color);
}

.searchInput {
  flex: 1;
  background-color: var(--bg-medium);
  border: 1px solid var(--bg-medium);
  padding-left: var(--space-lg);
  height: calc(100% - 2 * var(--space-sm));
  line-height: normal;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.searchInput:focus {
  background-color: var(--bg-white);
  box-shadow: 0 0 0 2px var(--primary-lighter);
  border-color: var(--primary-color);
}

.filterBtn {
  margin-left: var(--space-sm);
  background: none;
  border: none;
  color: var(--text-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  padding: 0;
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: background-color 0.2s ease;
}

.filterBtn:hover {
  background-color: var(--bg-light);
}

.filterBtn .navIcon {
  width: 24px;
  height: 24px;
}

/* 專案卡片列表樣式調整 */
.projectList {
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-md);
}

/* 專案卡片 */
.projectCard {
  background-color: var(--bg-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
}

.projectCard:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.cardContent {
  padding: var(--space-md);
}

.cardHeader {
  margin-bottom: var(--space-sm);
}

.cardTitle {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--space-sm);
  color: var(--text-dark);
}

.projectHighlights {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  margin-bottom: var(--space-sm);
}

.highlightItem {
  display: flex;
  align-items: center;
  background-color: var(--bg-light);
  border-radius: var(--radius-full);
  padding: 4px 10px;
}

.highlightItem::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--primary-color);
  margin-right: 6px;
}

.highlightText {
  font-size: var(--font-size-sm);
  color: var(--text-dark);
}

.highlightIcon {
  font-size: 24px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-light);
  border-radius: var(--radius-full);
}

/* 卡片描述 */
.cardDescription {
  color: var(--text-medium);
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  margin: var(--space-md) 0;
}

/* 圖片畫廊 */
.cardImageGalleryContainer {
  width: 100%;
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--bg-light);
  height: 200px; /* 使用具體數值，避免循環引用 */
}

.cardImageGallery {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  gap: 0;
  height: 100%;
  padding: 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
  align-items: center;
}

.cardImageGallery::-webkit-scrollbar {
  display: none;
}

.galleryImage {
  height: 100%;
  width: auto;
  max-width: none;
  object-fit: contain;
  scroll-snap-align: start;
  flex-shrink: 0;
}

/* 圖片導航點 */
.galleryDots {
  position: absolute;
  bottom: var(--space-sm);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--space-xs);
}

.galleryDot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.2s ease;
}

.galleryDotActive {
  background-color: var(--bg-white);
  transform: scale(1.2);
}

/* 卡片底部 */
.cardFooter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--border-color);
}

.projectPrice {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-dark);
}

/* 專案狀態標籤 */
.projectStatus {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.statusActive {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.statusOrderingEnded {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.statusArrived {
  background-color: var(--info-light);
  color: var(--info-color);
}

.statusCompleted {
  background-color: var(--text-light);
  color: var(--text-dark);
}

.statusPending {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

/* 加載更多按鈕 */
.loadMore {
  width: 100%;
  padding: var(--space-md);
  margin-top: var(--space-xl);
  background: none;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  color: var(--text-medium);
  font-size: var(--font-size-base);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.loadMore:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background-color: var(--primary-lighter);
}

/* 響應式設計 */
@media (max-width: 1200px) {
  .mainContent {
    max-width: 960px;
  }
}

@media (max-width: 992px) {
  .mainContent {
    max-width: 720px;
  }
}

@media (max-width: 768px) {
  .mainContent {
    max-width: 100%;
    padding-left: var(--space-md);
    padding-right: var(--space-md);
  }
  
  .projectList {
    grid-template-columns: 1fr;
  }
  
  .projectCard {
    padding: var(--space-lg);
  }

  .cardTitle {
    font-size: var(--font-size-xl);
  }

  .cardDescription {
    font-size: var(--font-size-base);
  }

  .projectHighlights {
    gap: var(--space-md);
  }

  .highlightIcon {
    width: 32px;
    height: 32px;
    font-size: 20px;
  }

  .highlightText {
    font-size: var(--font-size-sm);
  }

  .projectPrice {
    font-size: var(--font-size-lg);
  }
}

/* Home page specific CSS variables */
:global(:root) {
  --search-bar-height: 58px;
  --card-image-gallery-height: 200px;
  --card-spacing: var(--space-xl);
}

/* 覆蓋首頁主內容 padding */
:global(.main-content.page-home) {
  padding-bottom: calc(var(--bottom-nav-height) + var(--space-xl));
}

/* 覆蓋首頁 .container 排版與間距 */
:global(.page-home .container) {
  display: flex;
  flex-direction: column;
  gap: var(--card-spacing);
}

/* 覆蓋搜索按鈕樣式，還原 prototype filter-btn 規格 */
:global(.page-home .filter-btn) {
  margin-left: var(--space-sm);
  background: none;
  border: none;
  color: var(--text-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  padding: 0;
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: background-color 0.2s ease;
}

:global(.page-home .filter-btn:hover) {
  background-color: var(--bg-light);
}

/* 覆蓋首頁搜尋列樣式，完全比照 prototype */
:global(.page-home .search-container) {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  position: static;
  top: unset;
  left: unset;
  right: unset;
  z-index: auto;
}

:global(.page-home .search-input) {
  flex: 1;
  background-color: var(--bg-medium);
  border: 1px solid var(--bg-medium);
  padding-left: var(--space-lg);
  height: calc(100% - 2 * var(--space-sm));
  line-height: normal;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

:global(.page-home .search-input:focus) {
  background-color: var(--bg-white);
  box-shadow: 0 0 0 2px var(--primary-lighter);
  border-color: var(--primary-color);
}

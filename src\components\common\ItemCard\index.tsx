import React from 'react';
import type { Item } from '../../types';
import styles from './styles.module.css';

/**
 * 商品卡片元件屬性介面
 * 定義商品卡片元件的所有可配置屬性
 */
interface ItemCardProps {
  /**
   * 商品資料
   */
  readonly item: Item;
  /**
   * 商品數量
   */
  readonly quantity: number;
  /**
   * 最大可選數量
   * @default 99
   */
  readonly maxQuantity?: number;
  /**
   * 數量變更回調函數
   * 當用戶調整商品數量時觸發
   */
  readonly onQuantityChange: (item: Item, newQuantity: number) => void;
  /**
   * 是否禁用數量控制
   * 當設置為 true 時，用戶將無法調整商品數量
   * @default false
   */
  readonly disabled?: boolean;
}

/**
 * 商品卡片元件
 *
 * 用於顯示商品資訊並提供數量控制功能，適用於購物車、訂單頁面等場景。
 * 支持商品圖片、名稱、描述、價格顯示，以及數量的增減控制。
 * 可以顯示庫存狀態，並在庫存不足時提供視覺提示。
 *
 * 使用示例:
 * ```tsx
 * <ItemCard
 *   item={product}
 *   quantity={2}
 *   onQuantityChange={handleQuantityChange}
 *   maxQuantity={10}
 * />
 * ```
 *
 * @param props - 商品卡片元件屬性
 * @returns 商品卡片元件
 */
const ItemCard: React.FC<ItemCardProps> = ({
  item,
  quantity,
  maxQuantity = 99,
  onQuantityChange,
  disabled = false,
}) => {
  /**
   * 處理減少數量
   */
  const handleDecrease = (): void => {
    if (disabled || quantity <= 0) return;
    onQuantityChange(item, quantity - 1);
  };

  /**
   * 處理增加數量
   */
  const handleIncrease = (): void => {
    if (disabled || quantity >= maxQuantity) return;

    // 如果有庫存限制，確保不超過庫存
    if (item.stock !== undefined && quantity >= item.stock) return;

    onQuantityChange(item, quantity + 1);
  };

  /**
   * 處理輸入數量變更
   */
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    if (disabled) return;

    const newValue = parseInt(e.target.value, 10);

    if (isNaN(newValue)) {
      onQuantityChange(item, 0);
      return;
    }

    // 確保數量在有效範圍內
    let validQuantity = Math.max(0, newValue);
    validQuantity = Math.min(validQuantity, maxQuantity);

    // 如果有庫存限制，確保不超過庫存
    if (item.stock !== undefined) {
      validQuantity = Math.min(validQuantity, item.stock);
    }

    onQuantityChange(item, validQuantity);
  };

  /**
   * 格式化價格顯示
   */
  const formatPrice = (price: number): string => {
    return `$${price.toLocaleString()}`;
  };

  // 確定庫存狀態的樣式類名
  const getStockClassName = (): string => {
    if (item.stock === 0) return styles.outOfStock;
    if (item.stock <= 10) return styles.lowStock;
    return '';
  };

  return (
    <div className={`${styles.card} ${disabled ? styles.disabled : ''}`}>
      {item.imageUrl && (
        <div className={styles.imageContainer}>
          <img src={item.imageUrl} alt={item.name} className={styles.image} />
        </div>
      )}

      <div className={styles.info}>
        <h3 className={styles.name}>{item.name}</h3>
        <p className={styles.description}>{item.description}</p>

        {item.stock !== undefined && item.stock <= 10 && (
          <div className={`${styles.stock} ${getStockClassName()}`}>
            {item.stock === 0 ? '已售完' : `剩餘 ${item.stock} 件`}
          </div>
        )}
      </div>

      <div className={styles.price}>{formatPrice(item.price)}</div>

      <div className={styles.quantityControl}>
        <button
          className={styles.quantityBtn}
          onClick={handleDecrease}
          disabled={disabled || quantity <= 0}
          aria-label="減少數量"
        >
          -
        </button>

        <input
          type="number"
          className={styles.quantityInput}
          value={quantity}
          onChange={handleInputChange}
          min="0"
          max={item.stock !== undefined ? item.stock : maxQuantity}
          disabled={disabled}
          aria-label={`${item.name}數量`}
        />

        <button
          className={styles.quantityBtn}
          onClick={handleIncrease}
          disabled={
            disabled ||
            (item.stock !== undefined ? quantity >= item.stock : quantity >= maxQuantity)
          }
          aria-label="增加數量"
        >
          +
        </button>
      </div>
    </div>
  );
};

export default ItemCard;

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Paper, Typography, Button, Stack, Alert, CircularProgress } from '@mui/material';
import { Login as LoginIcon } from '@mui/icons-material';
import { supabase } from '@/lib/supabase';

/**
 * 開發模式的管理員帳號
 */
const DEV_ADMIN = {
  role: 'admin',
};

/**
 * 登入頁面（開發模式）
 */
const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleDevLogin = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('開始登入流程...');

      // 使用匿名登入
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'dev-password',
      });

      console.log('認證結果:', { authData, authError });

      if (authError) {
        throw new Error(`認證失敗：${authError.message}`);
      }

      if (!authData.user) {
        throw new Error('認證失敗：未取得用戶資料');
      }

      // 檢查開發者帳號
      let adminUser = null;
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id, role, display_name')
        .eq('id', authData.user.id)
        .limit(1)
        .maybeSingle();

      console.log('用戶資料檢查結果:', { userData, userError });

      if (userError) {
        throw new Error(`取得用戶資料失敗：${userError.message}`);
      }

      if (!userData) {
        // 如果找不到用戶資料，嘗試建立
        const { data: newUser, error: createError } = await supabase
          .from('users')
          .insert({
            id: authData.user.id,
            role: DEV_ADMIN.role,
            display_name: '開發者管理員',
            community_nickname: '開發者',
          })
          .select()
          .single();

        console.log('建立用戶結果:', { newUser, createError });

        if (createError) {
          throw new Error(`建立用戶資料失敗：${createError.message}`);
        }

        adminUser = newUser;
      } else {
        adminUser = userData;
      }

      if (adminUser.role !== 'admin') {
        throw new Error('用戶權限不足');
      }

      // 儲存登入狀態到 localStorage
      localStorage.setItem('isAdminLoggedIn', 'true');
      localStorage.setItem('adminUserId', authData.user.id);
      localStorage.setItem('adminRole', adminUser.role);
      localStorage.setItem('adminDisplayName', adminUser.display_name || '開發者管理員');

      // 更新最後登入時間
      const { error: updateError } = await supabase
        .from('users')
        .update({
          last_login_at: new Date().toISOString(),
        })
        .eq('id', authData.user.id);

      if (updateError) {
        console.error('更新登入時間失敗:', updateError);
      }

      console.log('登入成功，導向管理後台');
      navigate('/admin');
    } catch (err) {
      console.error('登入失敗:', err);
      setError(err instanceof Error ? err.message : '登入失敗');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.default',
        p: 2,
      }}
    >
      <Paper
        elevation={2}
        sx={{
          p: 4,
          width: '100%',
          maxWidth: 400,
          display: 'flex',
          flexDirection: 'column',
          gap: 3,
        }}
      >
        {/* 標題區域 */}
        <Box sx={{ textAlign: 'center' }}>
          <img
            src="/assets/images/forest-life-logo.png"
            alt="小森活"
            style={{ width: 64, height: 64, marginBottom: 16 }}
          />
          <Typography variant="h5" component="h1" gutterBottom>
            管理後台
          </Typography>
          <Typography variant="body2" color="text.secondary">
            開發模式登入
          </Typography>
        </Box>

        {/* 錯誤訊息 */}
        {error && (
          <Alert severity="error" sx={{ width: '100%' }}>
            {error}
          </Alert>
        )}

        {/* 登入按鈕 */}
        <Stack spacing={2}>
          <Button
            variant="contained"
            size="large"
            startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <LoginIcon />}
            onClick={handleDevLogin}
            disabled={isLoading}
            sx={{ mt: 2 }}
          >
            {isLoading ? '登入中...' : '開發者登入'}
          </Button>
        </Stack>

        {/* 說明文字 */}
        <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 2 }}>
          開發模式：自動使用管理員權限登入
        </Typography>
        <Alert severity="warning">注意：這是開發模式的登入方式，生產環境將使用 LINE 登入</Alert>
      </Paper>
    </Box>
  );
};

export default LoginPage;

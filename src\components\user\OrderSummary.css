/* OrderSummary styles for user frontend */

.order-summary {
  padding: var(--space-xl);
  background-color: var(--bg-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.order-summary .section-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin-bottom: var(--space-md);
  color: var(--text-dark);
  border-bottom: 2px solid var(--border-light);
  padding-bottom: var(--space-sm);
}

.order-summary .cart-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.order-summary .cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md) 0;
  border-bottom: 1px solid var(--border-light);
}

.order-summary .cart-item:last-child {
  border-bottom: none;
}

.order-summary .item-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.order-summary .item-name {
  font-weight: 500;
  color: var(--text-dark);
  font-size: var(--font-size-base);
}

.order-summary .item-quantity {
  font-size: var(--font-size-sm);
  color: var(--text-medium);
}

.order-summary .item-price {
  font-weight: 600;
  color: var(--text-dark);
  white-space: nowrap;
  min-width: 80px;
  text-align: right;
}

.order-summary .total-section {
  border-top: 2px solid var(--border-light);
  padding-top: var(--space-lg);
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-md);
}

.order-summary .total-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 350px;
  font-size: var(--font-size-base);
  padding: var(--space-xs) 0;
}

.order-summary .total-label {
  color: var(--text-medium);
}

.order-summary .total-amount {
  font-weight: 500;
  min-width: 100px;
  text-align: right;
}

.order-summary .final-total-row {
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin-top: var(--space-md);
  border-top: 2px solid var(--border-light);
  padding-top: var(--space-md);
  background-color: var(--bg-lighter);
  padding: var(--space-md);
  border-radius: var(--radius-md);
}

.order-summary .final-total-row .total-label,
.order-summary .final-total-row .total-amount {
  color: var(--text-dark);
}

.order-summary .actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-md);
  margin-top: var(--space-lg);
}

.order-summary .btn-primary {
  padding: var(--space-sm) var(--space-xl);
  font-size: var(--font-size-base);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.order-summary .discount-info {
  margin-top: calc(-1 * var(--space-lg) + var(--space-sm));
  margin-bottom: var(--space-md);
  font-size: var(--font-size-sm);
  color: var(--primary);
  text-align: right;
  font-weight: 500;
  min-height: 1.4em;
}

.order-summary .discount-row .total-label,
.order-summary .discount-row .total-amount {
  color: var(--error);
}

.order-summary .cart-empty-message {
  text-align: center;
  color: var(--text-light);
  padding: var(--space-xl);
  font-style: italic;
  border: 2px dashed var(--border-light);
  border-radius: var(--radius-lg);
  background-color: var(--bg-lighter);
}

.order-summary .cart-item-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  flex-grow: 1;
}

.order-summary .cart-item-name {
  font-weight: 500;
  color: var(--text-dark);
  font-size: var(--font-size-base);
}

.order-summary .cart-item-actions {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  flex-shrink: 0;
}

.order-summary .cart-item-price {
  font-weight: 600;
  color: var(--text-dark);
  white-space: nowrap;
  min-width: 80px;
  text-align: right;
}

.order-summary .remove-item-btn {
  border: 2px solid var(--error);
  color: var(--error);
  background-color: transparent;
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.order-summary .remove-item-btn:hover {
  background-color: var(--error-light);
  transform: translateY(-1px);
}

.order-summary .submission-message {
  margin-top: var(--space-md);
  text-align: right;
}

.order-summary .submission-message.success {
  color: var(--success-color);
}

.order-summary .submission-message.error {
  color: var(--error);
}

@import '../../../styles/variables.css';

.pageOrderHistory {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 70px;
}

.orderHistoryHeader {
  background: white;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.orderHistoryTitle {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
  padding: 8px 0;
}

.orderList {
  padding: 0 16px;
}

/* === 訂單歷史頁面主內容進階優化 === */
.container {
  padding-top: var(--space-lg);
  padding-bottom: var(--space-lg);
  max-width: 600px;
  margin: 0 auto;
}

.pageTitle {
  font-size: var(--font-size-xxxl);
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: var(--space-lg);
  letter-spacing: 1.5px;
  text-align: left;
}

/* === 專業 UI 設計優化搜尋過濾區塊 === */
.filterContainer {
  background: rgba(255,255,255,0.98);
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(60,138,63,0.06), 0 1.5px 6px rgba(0,0,0,0.03);
  padding: 48px 64px 32px 64px;
  margin-bottom: 48px;
  display: inline-block;
  min-width: 560px;
  width: auto;
  max-width: 96vw;
  transition: box-shadow 0.18s;
  margin-left: auto;
  margin-right: auto;
  gap: 24px;
}

.filterTitle {
  font-size: 1.25rem;
  font-weight: 700;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1.5px solid var(--border-light);
  color: var(--text-dark);
  text-align: left;
}

.filterForm {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  align-items: end;
}

.filterActions {
  grid-column: 3 / 4;
  display: flex;
  justify-content: flex-end;
  align-items: end;
  margin-top: 12px;
  padding-top: 0;
  border-top: none;
}

:global(.btn-primary) {
  min-width: 112px;
  height: 48px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 12px;
  background: var(--primary-color);
  color: #fff;
  box-shadow: 0 2px 8px rgba(87,172,90,0.08);
  transition: background 0.18s, box-shadow 0.18s, transform 0.13s;
  margin-left: 8px;
}
:global(.btn-primary:hover), :global(.btn-primary:focus) {
  background: var(--primary-hover);
  box-shadow: 0 4px 16px rgba(87,172,90,0.16);
  transform: translateY(-2px) scale(1.03);
}

:global(.form-input), :global(.form-select) {
  background: var(--bg-light);
  border: 1.5px solid var(--border-color);
  border-radius: 10px;
  font-size: 1rem;
  padding: 0 16px;
  height: 48px;
  font-weight: 500;
  color: var(--text-dark);
  transition: border 0.18s, box-shadow 0.18s;
  box-shadow: none;
}
:global(.form-input:focus), :global(.form-select:focus) {
  border-color: var(--primary-color);
  background: #fff;
  box-shadow: 0 0 0 2px var(--primary-light);
}
:global(.form-label) {
  color: var(--text-medium);
  font-size: 0.95rem;
  font-weight: 500;
  margin-bottom: 6px;
  letter-spacing: 0.2px;
}
:global(.filter-input::placeholder) {
  color: var(--text-light);
  opacity: 1;
  font-weight: 400;
}

.ordersContainer {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.orderCard {
  border-radius: var(--radius-lg);
  background: var(--bg-white);
  box-shadow: var(--shadow-md);
  transition: box-shadow 0.18s, transform 0.18s;
  overflow: hidden;
  border: 1px solid var(--border-light);
}
.orderCard:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px) scale(1.01);
}

.orderHeader {
  padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-md);
  background: var(--bg-white);
}

.orderIdDate {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.orderId {
  font-weight: 600;
  font-size: var(--font-size-base);
  letter-spacing: 0.5px;
}

.orderDate {
  font-size: var(--font-size-sm);
  color: var(--text-medium);
}

.orderStatus {
  flex-shrink: 0;
  min-width: 70px;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.orderBody {
  padding: var(--space-lg);
  background: var(--bg-white);
}

.orderProject {
  margin-bottom: var(--space-md);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid var(--border-light);
}

.projectImageContainer {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--bg-medium);
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
}

.projectImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-md);
}

.projectName {
  font-weight: 600;
  font-size: var(--font-size-md);
  margin-bottom: var(--space-xs);
  color: var(--text-dark);
}

.orderItems {
  margin-bottom: var(--space-lg);
}

.orderItem {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-sm) 0;
  color: var(--text-medium);
  font-size: var(--font-size-base);
  border-bottom: 1px dashed var(--border-light);
  transition: background 0.15s;
}
.orderItem:last-child {
  border-bottom: none;
}
.orderItem:hover {
  background: var(--primary-lighter);
}

.itemNameQty {
  display: flex;
  gap: var(--space-sm);
  margin-right: var(--space-md);
  align-items: center;
}

.itemQty {
  color: var(--text-light);
  font-size: var(--font-size-sm);
}

.itemPrice {
  font-weight: 500;
  color: var(--text-dark);
  white-space: nowrap;
}

.orderSummary {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-sm);
  padding-top: var(--space-md);
  margin-top: var(--space-md);
  border-top: 1px solid var(--border-light);
}

.summaryRow {
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 300px;
  gap: var(--space-md);
  font-size: var(--font-size-base);
}

.summaryLabel {
  color: var(--text-medium);
  text-align: left;
}

.summaryValue {
  font-weight: 500;
  min-width: 80px;
  text-align: right;
  color: var(--text-dark);
}

.totalRow {
  font-size: var(--font-size-md);
  font-weight: 600;
  margin-top: var(--space-xs);
}

.totalRow .summaryLabel,
.totalRow .summaryValue {
  color: var(--text-dark);
}

.orderFooter {
  padding: var(--space-md) var(--space-lg);
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: flex-end;
  gap: var(--space-md);
  background: var(--bg-white);
}

/* 空資料友善提示 */
.emptyOrders {
  color: var(--text-light);
  text-align: center;
  font-size: var(--font-size-md);
  padding: var(--space-xl) 0;
}

@media (max-width: 768px) {
  .container {
    max-width: 100%;
    padding-left: var(--space-md);
    padding-right: var(--space-md);
  }
  .filterContainer {
    padding: 18px 12px 12px 12px;
    border-radius: 12px;
    margin-bottom: 24px;
    min-width: 0;
    max-width: 100vw;
  }
  .filterForm {
    grid-template-columns: 1fr;
    gap: 18px;
  }
  .filterActions {
    grid-column: 1 / 2;
    justify-content: flex-end;
    margin-top: 12px;
  }
  :global(.btn-primary) {
    width: 100%;
    min-width: 0;
    margin-left: 0;
    font-size: 1.08rem;
    height: 46px;
  }
  .orderCard {
    border-radius: var(--radius-md);
  }
  .orderHeader, .orderFooter {
    padding: var(--space-md);
  }
  .orderBody {
    padding: var(--space-md);
  }
}

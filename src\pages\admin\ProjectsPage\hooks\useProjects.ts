/**
 * 小森活 - 專案管理 - useProjects hook
 *
 * 主要功能：
 * - 從 Supabase 讀取專案列表
 * - 支援篩選、分頁
 * - 提供重新載入功能
 *
 * 模組交互：
 * - 被 ProjectsPage 呼叫
 * - 回傳專案資料、loading 狀態、篩選條件、分頁資訊
 * - 篩選條件與分頁由 ProjectsPage 控制
 *
 * 已完成：
 * - 基本資料讀取
 * - 篩選條件
 * - 分頁
 *
 * 待辦：
 * - 篩選條件更多元（多欄位、多條件）
 * - 分頁優化
 * - 錯誤處理
 * - 整合排序
 */

import { useState, useEffect, useCallback } from 'react';
import { supabase, Project, handleSupabaseError } from '@/lib/supabase';
import { PostgrestFilterBuilder } from '@supabase/postgrest-js';

interface ProjectFilters {
  name?: string;
  project_status?: string;
  startDate?: Date;
  endDate?: Date;
}

interface SortConfig {
  column: keyof Project;
  direction: 'asc' | 'desc';
}

interface UseProjectsReturn {
  projects: Project[];
  totalCount: number;
  isLoading: boolean;
  error: Error | null;
  filters: ProjectFilters;
  setFilters: (filters: ProjectFilters) => void;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  pageSize: number;
  setPageSize: (size: number) => void;
  sort: SortConfig;
  setSort: (sort: SortConfig) => void;
  refresh: () => Promise<void>;
}

export const useProjects = (): UseProjectsReturn => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [filters, setFilters] = useState<ProjectFilters>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sort, setSort] = useState<SortConfig>({
    column: 'created_at',
    direction: 'desc',
  });

  const buildQuery = (query: PostgrestFilterBuilder<any, any, any>) => {
    // 套用篩選條件
    if (filters.name) {
      query = query.ilike('name', `%${filters.name}%`);
    }
    if (filters.project_status) {
      query = query.eq('project_status', filters.project_status);
    }
    if (filters.startDate) {
      query = query.gte('created_at', filters.startDate.toISOString());
    }
    if (filters.endDate) {
      query = query.lte('created_at', filters.endDate.toISOString());
    }

    // 套用排序
    query = query.order(sort.column, { ascending: sort.direction === 'asc' });

    // 套用分頁
    const from = (currentPage - 1) * pageSize;
    const to = from + pageSize - 1;
    query = query.range(from, to);

    return query;
  };

  const fetchProjects = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 取得總筆數，只套用篩選條件
      let countQuery = supabase.from('projects').select('*', { count: 'exact' });

      if (filters.name) {
        countQuery = countQuery.ilike('name', `%${filters.name}%`);
      }
      if (filters.project_status) {
        countQuery = countQuery.eq('project_status', filters.project_status);
      }
      if (filters.startDate) {
        countQuery = countQuery.gte('created_at', filters.startDate.toISOString());
      }
      if (filters.endDate) {
        countQuery = countQuery.lte('created_at', filters.endDate.toISOString());
      }

      const { count, error: countError } = await countQuery;

      if (countError) {
        throw countError;
      }

      setTotalCount(count || 0);

      // 取得分頁資料，套用篩選、排序、分頁
      const dataQuery = supabase.from('projects').select(`
        id,
        project_id_display,
        name,
        description,
        project_status,
        default_discount_id,
        created_at,
        updated_at,
        deadline,
        arrival_date,
        images,
        highlights
      `);

      const { data, error } = await buildQuery(dataQuery);

      if (error) {
        throw error;
      }

      // 自訂狀態排序
      const statusOrder = ['active', 'ordering_ended', 'arrived', 'completed'];
      const sorted = (data || []).sort((a, b) => {
        return statusOrder.indexOf(a.project_status) - statusOrder.indexOf(b.project_status);
      });
      setProjects(sorted);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('發生未知錯誤'));
    } finally {
      setIsLoading(false);
    }
  }, [filters, currentPage, pageSize, sort]);

  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  return {
    projects,
    totalCount,
    isLoading,
    error,
    filters,
    setFilters,
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    sort,
    setSort,
    refresh: fetchProjects,
  };
};

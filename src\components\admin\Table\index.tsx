import React from 'react';
import styles from './styles.module.css';

export interface Column<T = any> {
  key: string;
  title: string;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  width?: number | string;
}

export interface PaginationProps {
  current: number;
  pageSize: number;
  total: number;
  onChange: (page: number) => void;
}

export interface TableProps<T = any> {
  columns: Column<T>[];
  data: T[];
  loading?: boolean;
  rowKey?: string | ((record: T) => string);
  onRow?: (record: T, index: number) => React.HTMLAttributes<HTMLTableRowElement>;
  pagination?: PaginationProps;
}

export const Table = <T extends Record<string, any>>({
  columns,
  data,
  loading,
  rowKey = 'id',
  onRow,
  pagination,
}: TableProps<T>) => {
  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return record[rowKey]?.toString() || index.toString();
  };

  const totalPages = pagination ? Math.ceil(pagination.total / pagination.pageSize) : 0;

  return (
    <div className={styles.tableWrapper}>
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead>
            <tr>
              {columns.map(column => (
                <th key={column.key} className={styles.tableHeader} style={{ width: column.width }}>
                  {column.title}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={columns.length} style={{ textAlign: 'center', padding: '2rem' }}>
                  載入中...
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td colSpan={columns.length} style={{ textAlign: 'center', padding: '2rem' }}>
                  暫無數據
                </td>
              </tr>
            ) : (
              data.map((record, index) => (
                <tr
                  key={getRowKey(record, index)}
                  className={styles.tableRow}
                  {...(onRow?.(record, index) || {})}
                >
                  {columns.map(column => (
                    <td key={column.key} className={styles.tableCell} data-label={column.title}>
                      {column.render
                        ? column.render(record[column.key], record, index)
                        : record[column.key]}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {pagination && totalPages > 1 && (
        <div className={styles.pagination}>
          <button
            type="button"
            className={`${styles.pageButton} ${pagination.current === 1 ? styles.pageButtonDisabled : ''}`}
            disabled={pagination.current === 1}
            onClick={() => pagination.onChange(pagination.current - 1)}
          >
            &lt;
          </button>

          {[...Array(totalPages)].map((_, index) => {
            const pageNumber = index + 1;
            return (
              <button
                key={pageNumber}
                type="button"
                className={`${styles.pageButton} ${
                  pagination.current === pageNumber ? styles.pageButtonActive : ''
                }`}
                onClick={() => pagination.onChange(pageNumber)}
              >
                {pageNumber}
              </button>
            );
          })}

          <button
            type="button"
            className={`${styles.pageButton} ${
              pagination.current === totalPages ? styles.pageButtonDisabled : ''
            }`}
            disabled={pagination.current === totalPages}
            onClick={() => pagination.onChange(pagination.current + 1)}
          >
            &gt;
          </button>
        </div>
      )}
    </div>
  );
};

export default Table;

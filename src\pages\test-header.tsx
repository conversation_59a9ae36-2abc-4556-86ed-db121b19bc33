import React from 'react';

/**
 * 測試頁面 - 僅用於測試 header 垂直置中問題
 */
const TestHeaderPage: React.FC = () => {
  // 內聯樣式，完全隔離所有外部樣式影響
  const styles = {
    container: {
      padding: '100px 20px 20px',
      backgroundColor: '#f5f5f5',
      minHeight: '100vh',
    },
    header: {
      position: 'fixed' as const,
      top: 0,
      left: 0,
      right: 0,
      height: '60px',
      backgroundColor: '#2c4f4f',
      color: '#ffffff',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 16px',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    },
    logoContainer: {
      display: 'flex',
      alignItems: 'center',
      height: '100%',
      textDecoration: 'none',
    },
    logoImg: {
      width: '42px',
      height: '42px',
      marginRight: '8px',
      objectFit: 'contain' as const,
    },
    logoText: {
      fontSize: '1.25rem',
      fontWeight: 600,
      color: '#ffffff',
    },
    content: {
      marginTop: '20px',
      padding: '20px',
      backgroundColor: '#ffffff',
      borderRadius: '8px',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
    },
    heading: {
      fontSize: '1.5rem',
      marginBottom: '16px',
      color: '#2c4f4f',
    },
    paragraph: {
      fontSize: '1rem',
      lineHeight: 1.6,
      color: '#333',
    },
  };

  return (
    <div style={styles.container}>
      {/* 完全使用內聯樣式的 header */}
      <header style={styles.header}>
        <a href="/" style={styles.logoContainer}>
          <img src="/assets/images/forest-life-logo.png" alt="小森活 Logo" style={styles.logoImg} />
          <span style={styles.logoText}>小森活</span>
        </a>
      </header>

      {/* 頁面內容 */}
      <div style={styles.content}>
        <h1 style={styles.heading}>Header 垂直置中測試頁面</h1>
        <p style={styles.paragraph}>
          這個頁面用於測試 header 中 logo 的垂直置中問題。所有樣式都使用內聯方式定義，
          完全隔離外部樣式的影響。
        </p>
      </div>
    </div>
  );
};

export default TestHeaderPage;

import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, DatePicker, Upload, message, Button } from 'antd';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import type { UploadFile, RcFile } from 'antd/es/upload/interface';
import type { Project, Discount } from '@/lib/supabase';
import { supabase, generateDisplayIdByRPC } from '@/lib/supabase';
import dayjs from 'dayjs';
import * as XLSX from 'xlsx';

interface ProjectFormProps {
  project?: Project | null;
  discounts: Discount[];
  onSubmit: (values: Partial<Project>) => Promise<any>;
  onCancel: () => void;
}

const ProjectForm: React.FC<ProjectFormProps> = ({ project, discounts, onSubmit, onCancel }) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [parsedItems, setParsedItems] = useState<any[]>([]);

  useEffect(() => {
    if (project) {
      form.setFieldsValue({
        ...project,
        deadline: project.deadline ? dayjs(project.deadline) : null,
        arrival_date: project.arrival_date ? dayjs(project.arrival_date) : null,
      });
      if (project.images && project.images.length > 0) {
        setFileList(
          project.images.map((url, index) => ({
            uid: `-${index}`,
            name: url.split('/').pop() || 'image',
            status: 'done',
            url,
          }))
        );
      }
    }
  }, [project, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 折扣互斥驗證
      const hasSelectedDiscount = !!values.default_discount_id;
      const newDiscount = values.newDiscount || {};
      const hasNewDiscountInfo = newDiscount.name || newDiscount.threshold || newDiscount.value;

      if (hasSelectedDiscount && hasNewDiscountInfo) {
        message.error('請選擇現有折扣或定義新折扣，不能同時設定');
        return;
      }

      if (!hasSelectedDiscount && hasNewDiscountInfo) {
        if (!newDiscount.name?.trim()) {
          message.error('請輸入新折扣名稱');
          return;
        }
        if (!newDiscount.threshold || newDiscount.threshold <= 0) {
          message.error('請輸入有效的數量門檻');
          return;
        }
        if (newDiscount.value === undefined || newDiscount.value < 0) {
          message.error('請輸入有效的每件折扣金額');
          return;
        }
      }

      // 上傳圖片
      const uploadedImages = await Promise.all(
        fileList
          .filter(f => f.originFileObj)
          .map(async file => {
            const fileName = `${Date.now()}-${file.name}`;
            const { error } = await supabase.storage
              .from('projects')
              .upload(fileName, file.originFileObj as File);
            if (error) throw error;
            const { data } = supabase.storage.from('projects').getPublicUrl(fileName);
            return data.publicUrl;
          })
      );
      const existingImages = fileList.filter(f => !f.originFileObj).map(f => f.url as string);

      // 新折扣建立
      let discountId = values.default_discount_id;
      let discountDisplayId = '';
      if (!discountId && hasNewDiscountInfo) {
        const discountDisplayIdGenerated = await generateDisplayIdByRPC('D');
        const { data, error } = await supabase
          .from('discounts')
          .insert([
            {
              name: newDiscount.name,
              quantity_threshold: newDiscount.threshold,
              discount_per_item: newDiscount.value,
              description: newDiscount.description || '',
              start_date: newDiscount.startDate ? newDiscount.startDate.toISOString() : null,
              end_date: newDiscount.endDate ? newDiscount.endDate.toISOString() : null,
              active: newDiscount.active ?? true,
            },
          ])
          .select('id, name')
          .single();
        if (error) throw error;
        discountId = data.id;
        message.success(`新折扣已建立，名稱：${data.name}`);
      }

      // 產生專案顯示ID
      const projectDisplayId = await generateDisplayIdByRPC('P');
      form.setFieldValue('project_id_display', projectDisplayId);

      const submitData = {
        ...values,
        project_id_display: projectDisplayId,
        default_discount_id: discountId,
        images: [...existingImages, ...uploadedImages],
        deadline: values.deadline?.toISOString(),
        arrival_date: values.arrival_date?.toISOString(),
        highlights: values.highlights || [],
      };

      const savedProject = await onSubmit(submitData);

      if (parsedItems.length > 0 && savedProject?.id) {
        const itemsToInsert = await Promise.all(
          parsedItems.map(async item => {
            const itemDisplayId = await generateDisplayIdByRPC('I');
            return {
              project_id: savedProject.id,
              item_id_display: itemDisplayId,
              name: item.name,
              price: parseFloat(item.price),
              description: item.description || '',
              image_url: item.image_url || '',
            };
          })
        );
        const { error: itemError } = await supabase.from('items').insert(itemsToInsert);
        if (itemError) {
          console.error('商品匯入失敗:', itemError);
          message.error('商品匯入失敗');
        } else {
          message.success('商品匯入成功');
        }
      }
    } catch (err) {
      console.error('提交錯誤', err);
      message.error('提交失敗，請檢查欄位');
    }
  };

  const uploadProps = {
    onRemove: (file: UploadFile) => {
      const idx = fileList.indexOf(file);
      const newList = fileList.slice();
      newList.splice(idx, 1);
      setFileList(newList);
    },
    beforeUpload: (file: File) => {
      if (!['image/jpeg', 'image/png', 'image/gif'].includes(file.type)) {
        message.error('只能上傳圖片');
        return false;
      }
      if (file.size > 5 * 1024 * 1024) {
        message.error('圖片需小於5MB');
        return false;
      }
      const rcFile = {
        ...file,
        uid: Date.now().toString(),
        lastModifiedDate: new Date(),
      } as RcFile;
      const newFile: UploadFile = {
        uid: rcFile.uid,
        name: rcFile.name,
        status: 'done',
        originFileObj: rcFile,
      };
      setFileList([...fileList, newFile]);
      return false;
    },
    fileList,
  };

  const handleExcelUpload = async (file: File) => {
    try {
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data, { type: 'array' });

      // 解析專案資料
      const projectSheet = workbook.Sheets['Project'] || workbook.Sheets[workbook.SheetNames[0]];
      const projectJson = XLSX.utils.sheet_to_json<any>(projectSheet, { header: 1 });
      const header = projectJson[0];
      const valuesRow = projectJson[1] || [];
      const projectData: any = {};
      header.forEach((key: string, idx: number) => {
        projectData[key] = valuesRow[idx];
      });

      // 設定表單欄位
      form.setFieldsValue({
        name: projectData.name || '',
        description: projectData.description || '',
        project_status: projectData.status || 'active',
        deadline: projectData.deadline ? dayjs(projectData.deadline) : null,
        arrival_date: projectData.arrival_date ? dayjs(projectData.arrival_date) : null,
        highlights: [
          { icon: projectData.highlight1_icon || '🌱', text: projectData.highlight1_text || '' },
          { icon: projectData.highlight2_icon || '🌱', text: projectData.highlight2_text || '' },
          { icon: projectData.highlight3_icon || '🌱', text: projectData.highlight3_text || '' },
        ].filter(h => h.text),
        newDiscount: {
          name: projectData.discount_name || '',
          threshold: projectData.discount_threshold || '',
          value: projectData.discount_value || '',
          description: projectData.discount_description || '',
        },
      });

      // 解析商品資料
      const itemSheet = workbook.Sheets['Items'] || workbook.Sheets[workbook.SheetNames[1]];
      const items = XLSX.utils.sheet_to_json<any>(itemSheet, { defval: '' });
      setParsedItems(items);

      message.success('Excel解析成功');
    } catch (err) {
      console.error('Excel解析錯誤', err);
      message.error('Excel解析失敗');
    }
  };

  return (
    <Modal
      title={project ? '編輯專案' : '新增專案'}
      open={true}
      onOk={handleSubmit}
      onCancel={onCancel}
      width={800}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ project_status: 'active', highlights: [] }}
      >
        <Form.Item name="project_id_display" label="專案編號">
          <Input placeholder="儲存後自動生成" readOnly />
        </Form.Item>

        <Form.Item
          name="name"
          label="專案名稱"
          rules={[{ required: true, message: '請輸入專案名稱' }]}
        >
          <Input placeholder="請輸入專案名稱" />
        </Form.Item>

        <Form.Item name="description" label="專案描述">
          <Input.TextArea rows={3} placeholder="請輸入專案描述" />
        </Form.Item>

        <Form.Item
          name="project_status"
          label="專案狀態"
          rules={[{ required: true, message: '請選擇狀態' }]}
        >
          <Select>
            <Select.Option value="active">進行中</Select.Option>
            <Select.Option value="ordering_ended">結束預購</Select.Option>
            <Select.Option value="arrived">已到貨</Select.Option>
            <Select.Option value="completed">已完成</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item name="default_discount_id" label="預設折扣">
          <Select allowClear placeholder="選擇現有折扣">
            {discounts.map(d => (
              <Select.Option key={d.id} value={d.id}>
                {d.name}
              </Select.Option>
            ))}
          </Select>
          <div style={{ marginTop: 8 }}>
            <a href="/admin/discounts" target="_blank" rel="noopener noreferrer">
              前往折扣管理
            </a>
          </div>
        </Form.Item>

        <Form.Item name="deadline" label="截止日期">
          <DatePicker style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item name="arrival_date" label="預計到貨日期">
          <DatePicker style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item label="專案亮點">
          <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
            {(form.getFieldValue('highlights') || []).map((item: any, idx: number) => (
              <div key={idx} style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
                <Button
                  onClick={() => {
                    const container = document.createElement('div');
                    container.style.position = 'fixed';
                    container.style.zIndex = '10000';
                    container.style.top = '50%';
                    container.style.left = '50%';
                    container.style.transform = 'translate(-50%, -50%)';
                    container.style.background = '#fff';
                    container.style.border = '1px solid #ccc';
                    container.style.borderRadius = '8px';
                    container.style.padding = '12px';
                    container.style.maxHeight = '400px';
                    container.style.overflowY = 'auto';
                    container.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';

                    const categories: Record<string, string[]> = {
                      自然植物: [
                        '🌱',
                        '🌿',
                        '🍃',
                        '🌺',
                        '🌸',
                        '🌼',
                        '🌻',
                        '🌹',
                        '🍀',
                        '🌳',
                        '🌲',
                        '🌴',
                        '🍂',
                        '🍁',
                      ],
                      食物飲品: [
                        '🍎',
                        '🍊',
                        '🍇',
                        '🍓',
                        '🥝',
                        '🥕',
                        '🥬',
                        '🥦',
                        '🥗',
                        '🌾',
                        '🍚',
                        '🥖',
                        '🥨',
                        '🥐',
                        '🥯',
                        '🥞',
                        '🧀',
                        '🥩',
                        '🥪',
                      ],
                      運輸物流: ['📦', '🚚', '🚛', '✈️', '🚲', '🛵', '🏃', '⚡'],
                      環保永續: ['♻️', '🌍', '🌱', '💚', '🌞', '💧', '🍃', '🧴'],
                      品質保證: ['⭐', '✨', '💯', '✅', '👍', '🏆', '💪', '📋'],
                      時間相關: ['⏰', '📅', '⌛', '🕒', '📆', '🗓️'],
                      服務相關: ['🎁', '💝', '🎯', '💡', '🔔', '📱', '💳', '🤝'],
                    };

                    Object.entries(categories).forEach(([title, emojis]) => {
                      const titleEl = document.createElement('div');
                      titleEl.style.fontWeight = 'bold';
                      titleEl.style.margin = '8px 0 4px';
                      titleEl.innerText = title;
                      container.appendChild(titleEl);

                      const row = document.createElement('div');
                      row.style.display = 'flex';
                      row.style.flexWrap = 'wrap';
                      row.style.gap = '4px';

                      emojis.forEach(emoji => {
                        const btn = document.createElement('button');
                        btn.type = 'button';
                        btn.style.fontSize = '20px';
                        btn.style.padding = '4px';
                        btn.style.border = '1px solid #ccc';
                        btn.style.borderRadius = '4px';
                        btn.style.cursor = 'pointer';
                        btn.innerText = emoji;
                        btn.onclick = () => {
                          const highlights = form.getFieldValue('highlights') || [];
                          highlights[idx].icon = emoji;
                          form.setFieldValue('highlights', [...highlights]);
                          document.body.removeChild(container);
                        };
                        row.appendChild(btn);
                      });

                      container.appendChild(row);
                    });

                    const cancelBtn = document.createElement('button');
                    cancelBtn.innerText = '取消';
                    cancelBtn.style.marginTop = '12px';
                    cancelBtn.style.padding = '6px 12px';
                    cancelBtn.style.border = '1px solid #ccc';
                    cancelBtn.style.borderRadius = '4px';
                    cancelBtn.style.cursor = 'pointer';
                    cancelBtn.onclick = () => {
                      document.body.removeChild(container);
                    };
                    container.appendChild(cancelBtn);

                    document.body.appendChild(container);
                  }}
                >
                  {item.icon || '🌱'}
                </Button>
                <Input
                  placeholder="亮點描述"
                  value={item.text || ''}
                  onChange={e => {
                    const highlights = form.getFieldValue('highlights') || [];
                    highlights[idx].text = e.target.value;
                    form.setFieldValue('highlights', [...highlights]);
                  }}
                />
                <Button
                  danger
                  onClick={() => {
                    const highlights = form.getFieldValue('highlights') || [];
                    highlights.splice(idx, 1);
                    form.setFieldValue('highlights', [...highlights]);
                  }}
                >
                  刪除
                </Button>
              </div>
            ))}
            <Button
              disabled={(form.getFieldValue('highlights') || []).length >= 3}
              onClick={() => {
                const highlights = form.getFieldValue('highlights') || [];
                highlights.push({ icon: '🌱', text: '' });
                form.setFieldValue('highlights', highlights);
              }}
            >
              新增亮點
            </Button>
          </div>
        </Form.Item>

        <Form.Item label="專案圖片" extra="支援多張圖片上傳，單張小於5MB">
          <Upload listType="picture-card" {...uploadProps}>
            {fileList.length >= 8 ? null : (
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上傳</div>
              </div>
            )}
          </Upload>
        </Form.Item>

        <div style={{ border: '1px dashed #ccc', padding: 12, borderRadius: 8, marginTop: 16 }}>
          <div style={{ fontWeight: 'bold', marginBottom: 8 }}>上傳專案Excel檔案</div>
          <Upload
            accept=".xlsx"
            showUploadList={false}
            beforeUpload={file => {
              handleExcelUpload(file);
              return false;
            }}
          >
            <Button icon={<UploadOutlined />}>選擇Excel檔案</Button>
          </Upload>
          <div style={{ fontSize: 12, color: '#888', marginTop: 4 }}>
            只支援.xlsx，內含專案與商品資料
          </div>
          <div style={{ marginTop: 8, background: '#f9f9f9', padding: 8, borderRadius: 4 }}>
            <div style={{ fontWeight: 'bold' }}>商品預覽</div>
            {parsedItems.length === 0 && <div>尚未上傳或解析中...</div>}
            {parsedItems.map((item, idx) => (
              <div key={idx}>
                {item.name} - ${item.price} - {item.description}
              </div>
            ))}
          </div>
        </div>
      </Form>
    </Modal>
  );
};

export default ProjectForm;

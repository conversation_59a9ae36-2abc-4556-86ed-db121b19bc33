import { createTheme } from '@mui/material/styles';

// 擴展 Theme 介面以包含自定義屬性
declare module '@mui/material/styles' {
  interface Theme {
    customVariables: {
      layout: {
        headerHeight: number;
        footerHeight: number;
        sidebarWidth: number;
        containerWidth: number;
      };
      transitions: {
        fast: string;
        normal: string;
        slow: string;
      };
    };
  }
  interface ThemeOptions {
    customVariables?: {
      layout?: {
        headerHeight?: number;
        footerHeight?: number;
        sidebarWidth?: number;
        containerWidth?: number;
      };
      transitions?: {
        fast?: string;
        normal?: string;
        slow?: string;
      };
    };
  }
}

/**
 * 小森活應用主題配置
 * 定義全局顏色、字體、間距等主題變數
 */
export const theme = createTheme({
  palette: {
    primary: {
      main: '#2c4f4f',
      light: '#4c6e6e',
      dark: '#1c3f3f',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#FFC75A',
      light: '#FFD78A',
      dark: '#DFA73A',
      contrastText: '#704800',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
    text: {
      primary: '#333333',
      secondary: '#666666',
    },
  },
  typography: {
    fontFamily: '"Noto Sans TC", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2rem',
      fontWeight: 600,
    },
    h2: {
      fontSize: '1.5rem',
      fontWeight: 600,
    },
    h3: {
      fontSize: '1.25rem',
      fontWeight: 600,
    },
    body1: {
      fontSize: '1rem',
    },
    body2: {
      fontSize: '0.875rem',
    },
  },
  spacing: 8, // 基礎間距單位為 8px
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        },
      },
    },
  },
  customVariables: {
    layout: {
      headerHeight: 60,
      footerHeight: 56,
      containerWidth: 1200,
      sidebarWidth: 0, // 前台不使用側邊欄
    },
    transitions: {
      fast: 'all 0.2s ease',
      normal: 'all 0.3s ease',
      slow: 'all 0.5s ease',
    },
  },
});

/**
 * 後台主題配置
 * 使用不同的顏色方案和佈局設定
 */
export const adminTheme = createTheme({
  palette: {
    primary: {
      main: '#1976D2',
      light: '#63A4FF',
      dark: '#004BA0',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#2E7D32',
      light: '#60AD5E',
      dark: '#005005',
      contrastText: '#ffffff',
    },
    success: {
      main: '#2E7D32',
      light: '#60AD5E',
      dark: '#005005',
    },
    warning: {
      main: '#ED6C02',
      light: '#FFB74D',
      dark: '#E65100',
    },
    error: {
      main: '#D32F2F',
      light: '#EF5350',
      dark: '#C62828',
    },
    info: {
      main: '#0288D1',
      light: '#03A9F4',
      dark: '#01579B',
    },
    background: {
      default: '#f7f9fc',
      paper: '#ffffff',
    },
    text: {
      primary: '#333333',
      secondary: '#666666',
    },
  },
  typography: {
    fontFamily: '"Noto Sans TC", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2rem',
      fontWeight: 600,
    },
    h2: {
      fontSize: '1.5rem',
      fontWeight: 600,
    },
    h3: {
      fontSize: '1.25rem',
      fontWeight: 600,
    },
    body1: {
      fontSize: '1rem',
    },
    body2: {
      fontSize: '0.875rem',
    },
  },
  spacing: 8,
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          borderRight: '1px solid rgba(0, 0, 0, 0.12)',
        },
      },
    },
  },
  customVariables: {
    layout: {
      headerHeight: 64,
      footerHeight: 0, // 後台不使用底部導航
      sidebarWidth: 240,
      containerWidth: 1400,
    },
    transitions: {
      fast: 'all 0.2s ease',
      normal: 'all 0.3s ease',
      slow: 'all 0.5s ease',
    },
  },
});

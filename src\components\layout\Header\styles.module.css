/**
 * Header 元件模組化樣式
 * 使用 CSS Module 確保樣式隔離，避免樣式衝突
 */

/* 引入全局變數 */
@import '../../../styles/variables.css';

/* Header 容器 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--app-header-height);
  background-color: var(--primary);
  color: var(--primary-text);
  z-index: var(--z-index-fixed);
  box-shadow: var(--shadow-sm);
}

/* Header 內容容器 */
.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 var(--space-md);
  max-width: var(--container-width);
  margin: 0 auto;
}

/* Logo 容器 */
.logoContainer {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
}

/* Logo 圖片 */
.logoImg {
  width: 42px;
  height: 42px;
  margin-right: var(--space-sm);
  object-fit: contain;
}

/* Logo 文字 */
.logoText {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--primary-text);
}

/* 導航區域 */
.navSection {
  display: flex;
  align-items: center;
}

/* 搜索表單 */
.searchForm {
  position: relative;
  margin: 0 var(--space-md);
  max-width: 300px;
  width: 100%;
}

/* 搜索輸入框 */
.searchInput {
  width: 100%;
  height: 36px;
  padding: 0 var(--space-lg) 0 var(--space-md);
  border-radius: var(--radius-full);
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--primary-text);
  border: none;
  outline: none;
  transition: background-color 0.2s ease;
}

.searchInput::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.searchInput:hover,
.searchInput:focus {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 搜索按鈕 */
.searchButton {
  position: absolute;
  right: var(--space-xs);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--primary-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.searchButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 用戶操作區域 */
.userActions {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

/* 圖標按鈕 */
.iconButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: none;
  border: none;
  color: var(--primary-text);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.iconButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 購物車徽章 */
.cartBadge {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 18px;
  height: 18px;
  padding: 0 var(--space-xs);
  border-radius: var(--radius-full);
  background-color: var(--secondary);
  color: var(--secondary-text);
  font-size: var(--font-size-xs);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .searchForm {
    max-width: 200px;
  }
}

@media (max-width: 576px) {
  .logoText {
    display: none;
  }
  
  .searchForm {
    max-width: 150px;
  }
}

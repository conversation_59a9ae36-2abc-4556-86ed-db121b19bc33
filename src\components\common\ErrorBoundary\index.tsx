import { Component, ErrorInfo, ReactNode } from 'react';
import styles from './styles.module.css';

interface Props {
  /** 子元件 */
  children: ReactNode;
  /** 自定義錯誤顯示元件 */
  fallback?: ReactNode;
}

interface State {
  /** 是否發生錯誤 */
  hasError: boolean;
  /** 錯誤資訊 */
  error: Error | null;
}

/**
 * 錯誤邊界元件
 *
 * 用於捕獲子元件中的 JavaScript 錯誤，並顯示備用 UI
 */
export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('錯誤邊界捕獲到錯誤:', error);
    console.error('錯誤堆疊:', errorInfo);
  }

  public render() {
    const { hasError, error } = this.state;
    const { children, fallback } = this.props;

    if (hasError) {
      if (fallback) {
        return fallback;
      }

      return (
        <div className={styles.errorContainer}>
          <div className={styles.errorContent}>
            <h2 className={styles.errorTitle}>發生錯誤</h2>
            <p className={styles.errorMessage}>{error?.message || '應用程式發生未知錯誤'}</p>
            <button className={styles.retryButton} onClick={() => window.location.reload()}>
              重新載入頁面
            </button>
          </div>
        </div>
      );
    }

    return children;
  }
}

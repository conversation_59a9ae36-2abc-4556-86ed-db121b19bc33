import { useState } from 'react';
import { getCloudinaryUploadUrl } from '../lib/cloudinary';

interface UploadOptions {
  maxSizeMB?: number;
  acceptedFileTypes?: string[];
}

interface UploadResult {
  url: string;
  publicId: string;
  width: number;
  height: number;
}

export const useCloudinaryUpload = (options: UploadOptions = {}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const validateFile = (file: File): string | null => {
    // 檢查文件大小
    if (options.maxSizeMB && file.size > options.maxSizeMB * 1024 * 1024) {
      return `檔案大小不能超過 ${options.maxSizeMB}MB`;
    }

    // 檢查文件類型
    if (options.acceptedFileTypes && !options.acceptedFileTypes.includes(file.type)) {
      return `只接受 ${options.acceptedFileTypes.join(', ')} 格式的檔案`;
    }

    return null;
  };

  const uploadFile = async (file: File): Promise<UploadResult> => {
    setIsUploading(true);
    setProgress(0);
    setError(null);

    try {
      // 驗證文件
      const validationError = validateFile(file);
      if (validationError) {
        throw new Error(validationError);
      }

      // 準備表單數據
      const formData = new FormData();
      formData.append('file', file);
      formData.append('upload_preset', import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET);

      // 上傳文件
      const response = await fetch(getCloudinaryUploadUrl(), {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('上傳失敗');
      }

      const data = await response.json();

      return {
        url: data.secure_url,
        publicId: data.public_id,
        width: data.width,
        height: data.height,
      };
    } catch (err) {
      setError(err instanceof Error ? err.message : '上傳過程發生錯誤');
      throw err;
    } finally {
      setIsUploading(false);
      setProgress(100);
    }
  };

  return {
    uploadFile,
    isUploading,
    progress,
    error,
  };
};

/* SearchBar styles for user frontend */

.page-home .search-container {
  position: fixed;
  top: var(--app-header-height);
  left: 0;
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  background: var(--bg-white);
  display: flex;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  z-index: 999;
  height: var(--search-bar-height);
  border-bottom: 1px solid var(--border-color);
}

.page-home .search-input {
  flex: 1;
  background-color: var(--bg-medium);
  border: 1px solid var(--bg-medium);
  padding-left: var(--space-lg);
  height: calc(100% - 2 * var(--space-sm));
  line-height: normal;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.page-home .search-input:focus {
  background-color: var(--bg-white);
  box-shadow: 0 0 0 2px var(--primary-lighter);
  border-color: var(--primary-color);
}

.page-home .filter-btn {
  margin-left: var(--space-sm);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: var(--font-size-base);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--primary-light);
  color: var(--primary-contrast);
  width: 40px;
  height: 40px;
  position: relative;
}

.page-home .filter-btn:hover {
  background-color: var(--primary);
  color: var(--primary-contrast);
}

.page-home .filter-btn .nav-icon {
  width: 24px;
  height: 24px;
}

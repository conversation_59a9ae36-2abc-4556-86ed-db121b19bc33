@import '../../../styles/variables.css';

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  animation: fadeIn var(--transition-fast);
}

.popup {
  background-color: var(--bg-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 400px;
  padding: var(--space-lg);
  position: relative;
  animation: slideUp var(--transition-normal);
}

.closeBtn {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-medium);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  transition: background-color var(--transition-fast), color var(--transition-fast);
}

.closeBtn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-dark);
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-full);
  background-color: var(--primary-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  font-weight: 600;
  overflow: hidden;
  margin-bottom: var(--space-md);
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.form {
  margin-bottom: var(--space-lg);
}

.errorMessage {
  color: var(--error-color);
  font-size: var(--font-size-sm);
  margin-top: var(--space-xs);
}

.successMessage {
  color: var(--success-color);
  font-size: var(--font-size-sm);
  margin-top: var(--space-xs);
}

.actions {
  border-top: 1px solid #eee;
  padding-top: var(--space-lg);
}

.logoutBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  color: var(--error-color);
  border-color: var(--error-color);
}

.logoutBtn:hover {
  background-color: rgba(231, 76, 60, 0.05);
}

.logoutIcon {
  width: 18px;
  height: 18px;
  margin-right: var(--space-xs);
}

/* 動畫 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

import React, { useState, useEffect, useRef } from 'react';
import styles from './styles.module.css';

/**
 * 用戶資料彈出視窗屬性介面
 * 定義用戶資料彈出視窗的所有可配置屬性
 */
interface ProfilePopupProps {
  /**
   * 是否顯示彈出視窗
   */
  readonly isOpen: boolean;
  /**
   * 關閉彈出視窗的回調函數
   */
  readonly onClose: () => void;
  /**
   * 用戶暱稱
   * @default ''
   */
  readonly nickname?: string;
  /**
   * 用戶頭像 URL
   */
  readonly avatarUrl?: string;
  /**
   * 更新暱稱的回調函數
   * 返回 Promise 以支持非同步操作
   */
  readonly onUpdateNickname?: (nickname: string) => Promise<void>;
  /**
   * 登出的回調函數
   */
  readonly onLogout?: () => void;
}

/**
 * 用戶資料彈出視窗元件
 *
 * 用於顯示用戶資料和設置選項，包括修改暱稱和登出功能。
 * 支持非同步更新暱稱，並提供錯誤處理和成功提示。
 * 點擊視窗外部或關閉按鈕可關閉視窗。
 *
 * 使用示例:
 * ```tsx
 * <ProfilePopup
 *   isOpen={isProfileOpen}
 *   onClose={handleCloseProfile}
 *   nickname={user.nickname}
 *   avatarUrl={user.avatarUrl}
 *   onUpdateNickname={handleUpdateNickname}
 *   onLogout={handleLogout}
 * />
 * ```
 *
 * @param props - 用戶資料彈出視窗屬性
 * @returns 用戶資料彈出視窗元件
 */
const ProfilePopup: React.FC<ProfilePopupProps> = ({
  isOpen,
  onClose,
  nickname = '',
  avatarUrl,
  onUpdateNickname,
  onLogout,
}) => {
  const [communityNickname, setCommunityNickname] = useState<string>(nickname);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string>('');
  const popupRef = useRef<HTMLDivElement>(null);

  // 當 nickname prop 變化時更新 communityNickname 狀態
  useEffect(() => {
    setCommunityNickname(nickname);
  }, [nickname]);

  // 點擊外部關閉彈出視窗
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent): void => {
      if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  /**
   * 處理暱稱表單提交
   */
  const handleSubmit = async (event: React.FormEvent): Promise<void> => {
    event.preventDefault();

    // 清除之前的消息
    setErrorMessage('');
    setSuccessMessage('');

    // 驗證暱稱
    if (!communityNickname.trim()) {
      setErrorMessage('請輸入暱稱');
      return;
    }

    if (communityNickname.length > 20) {
      setErrorMessage('暱稱不能超過20個字符');
      return;
    }

    if (onUpdateNickname) {
      try {
        setIsSubmitting(true);
        await onUpdateNickname(communityNickname.trim());
        setSuccessMessage('暱稱更新成功');
      } catch (error) {
        setErrorMessage('更新暱稱時發生錯誤，請稍後再試');
        console.error('更新暱稱錯誤:', error);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  /**
   * 處理登出
   */
  const handleLogout = (): void => {
    if (onLogout) {
      onLogout();
    }
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className={styles.overlay}>
      <div className={styles.popup} ref={popupRef}>
        <button className={styles.closeBtn} onClick={onClose} aria-label="關閉">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="18" y1="6" x2="6" y2="18" />
            <line x1="6" y1="6" x2="18" y2="18" />
          </svg>
        </button>

        <div className={styles.header}>
          <div className={styles.avatar}>
            {avatarUrl ? (
              <img src={avatarUrl} alt="用戶頭像" />
            ) : (
              <span>{communityNickname.charAt(0) || 'U'}</span>
            )}
          </div>
        </div>

        <form className={styles.form} onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="communityNickname" className="form-label">
              社群暱稱
            </label>
            <input
              type="text"
              id="communityNickname"
              className="form-input"
              placeholder="設定您在小森活的暱稱"
              value={communityNickname}
              onChange={e => setCommunityNickname(e.target.value)}
              disabled={isSubmitting}
            />
            {errorMessage && <div className={styles.errorMessage}>{errorMessage}</div>}
            {successMessage && <div className={styles.successMessage}>{successMessage}</div>}
          </div>

          <button type="submit" className="btn btn-primary" disabled={isSubmitting}>
            {isSubmitting ? '更新中...' : '更新暱稱'}
          </button>
        </form>

        <div className={styles.actions}>
          <button className={`btn btn-outline ${styles.logoutBtn}`} onClick={handleLogout}>
            <svg
              className={styles.logoutIcon}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
              <polyline points="16 17 21 12 16 7" />
              <line x1="21" y1="12" x2="9" y2="12" />
            </svg>
            登出
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProfilePopup;

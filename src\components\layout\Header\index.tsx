import React, { useState } from 'react';
import { AppBar, Toolbar, Typography } from '@mui/material';
import { Link } from 'react-router-dom';
import SearchIcon from '@mui/icons-material/Search';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import styles from './styles.module.css';

/**
 * Header 元件屬性介面
 */
interface HeaderProps {
  /**
   * 是否顯示購物車按鈕
   */
  showCartButton?: boolean;
  /**
   * 購物車中的商品數量
   */
  cartItemCount?: number;
  /**
   * 點擊購物車按鈕時的回調函數
   */
  onCartClick?: () => void;
  /**
   * 點擊用戶頭像時的回調函數
   */
  onUserProfileClick?: () => void;
  /**
   * 點擊搜索按鈕時的回調函數
   */
  onSearchSubmit?: (searchTerm: string) => void;
}

/**
 * Header 元件 - 顯示在所有頁面頂部的導航欄
 * 使用 CSS Module 確保樣式隔離
 */
const Header: React.FC<HeaderProps> = ({
  showCartButton = true,
  cartItemCount = 0,
  onCartClick,
  onUserProfileClick,
  onSearchSubmit,
}) => {
  const [searchTerm, setSearchTerm] = useState<string>('');

  /**
   * 處理搜索表單提交
   */
  const handleSearchSubmit = (event: React.FormEvent): void => {
    event.preventDefault();
    if (onSearchSubmit && searchTerm.trim()) {
      onSearchSubmit(searchTerm.trim());
    }
  };

  /**
   * 處理搜索輸入變化
   */
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setSearchTerm(event.target.value);
  };

  return (
    <header className="app-header">
      <Link to="/" className="logo">
        <img src="/assets/images/forest-life-logo.png" alt="小森活 Logo" className="logo-img" />
        <span className="logo-text">小森活</span>
      </Link>
      <div className="nav-right">
        {showCartButton && (
          <button
            className="cart-btn"
            onClick={onCartClick}
            aria-label="購物車"
            style={{ position: 'relative' }}
          >
            <span className="nav-icon cart-icon"></span>
            {cartItemCount > 0 && (
              <div className="cart-badge">{cartItemCount > 99 ? '99+' : cartItemCount}</div>
            )}
          </button>
        )}
        <button className="user-control" onClick={onUserProfileClick} aria-label="使用者中心">
          <div className="user-avatar">
            <span>U</span>
          </div>
        </button>
      </div>
    </header>
  );
};

export default Header;

/* 用戶管理頁面樣式 - 根據原型設計修正 */
.usersContainer {
  width: 100%;
  padding: 24px;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.pageTitle {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--admin-text-dark);
}

.headerActions {
  display: flex;
  gap: var(--space-md);
}

.searchInput {
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  width: 240px;
  font-size: 0.95rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.searchInput:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
  outline: none;
}

.filterSelect {
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.95rem;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.filterSelect:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
  outline: none;
}

.userList {
  width: 100%;
  overflow: hidden;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

.userTable {
  width: 100%;
  border-collapse: collapse;
}

.userTableHeader {
  padding: var(--space-md);
  text-align: left;
  font-weight: 600;
  color: var(--text-dark);
  border-bottom: 2px solid var(--border-color);
  background-color: var(--bg-light);
}

.userTableCell {
  padding: var(--space-md);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.95rem;
  color: var(--text-dark);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.userTableRow {
  transition: background-color 0.2s;
}

.userTableRow:hover {
  background-color: var(--primary-light);
}

.userTableRow:last-child .userTableCell {
  border-bottom: none;
}

.roleBadge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  line-height: var(--line-height-tight);
}

.roleAdmin {
  background-color: var(--info-light);
  color: var(--info-color);
}

.roleUser {
  background-color: var(--bg-medium);
  color: var(--text-medium);
}

.actionButton {
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: transparent;
  color: var(--text-medium);
  cursor: pointer;
  transition: all 0.2s;
  margin-right: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.viewButton:hover {
  background-color: var(--info-light);
  color: var(--info-color);
  border-color: var(--info-color);
}

.editButton:hover {
  background-color: var(--warning-light);
  color: var(--warning-color);
  border-color: var(--warning-color);
}

.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: var(--space-lg);
  gap: var(--space-sm);
}

.pageButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: transparent;
  color: var(--text-medium);
  cursor: pointer;
  transition: all 0.2s;
}

.pageButton:hover {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.pageButtonActive {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pageButtonDisabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageButtonDisabled:hover {
  background-color: transparent;
  color: var(--text-medium);
  border-color: var(--border-color);
}

.filterContainer {
  margin-bottom: var(--space-lg);
  padding: 0;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

.filterHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.filterTitle {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-dark);
}

.filterRow {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-md);
  align-items: flex-end;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  flex: 1;
  min-width: 200px;
}

.filterLabel {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-medium);
}

.filterInput {
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.95rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.filterInput:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
  outline: none;
}

.filterButton {
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: transparent;
  color: var(--text-medium);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
}

.filterButton:hover {
  background-color: var(--bg-medium);
  border-color: var(--border-color-dark);
  color: var(--text-dark);
}

.filterButtonPrimary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.filterButtonPrimary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  color: white;
}

.addButton {
  padding: var(--space-sm) var(--space-md);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: var(--shadow-sm);
}

.addButton:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.addButton:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.actionButtonDanger {
  color: var(--danger-color);
  border-color: var(--danger-color);
}

.actionButtonDanger:hover {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

/* 角色標籤 */
.roleadmin {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  background-color: var(--admin-primary-lighter);
  color: var(--admin-primary-darker);
}

.rolevendor {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  background-color: var(--admin-secondary-lighter);
  color: var(--admin-secondary-darker);
}

.roleuser {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  background-color: var(--admin-gray-100);
  color: var(--admin-gray-700);
}

/* 狀態標籤 */
.statusactive {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  background-color: var(--admin-success-lighter);
  color: var(--admin-success-darker);
}

.statusinactive {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  background-color: var(--admin-warning-lighter);
  color: var(--admin-warning-darker);
}

.statussuspended {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  background-color: var(--admin-danger-lighter);
  color: var(--admin-danger-darker);
}

/* 操作按鈕容器 */
.actionButtons {
  display: flex;
  gap: 0.5rem;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .headerActions {
    flex-direction: column;
    gap: var(--space-sm);
  }
  
  .searchInput,
  .filterSelect {
    width: 100%;
  }
  
  .userTableHeader {
    display: none;
  }
  
  .userTableRow {
    display: block;
    padding: var(--space-md);
    position: relative;
    margin-bottom: var(--space-sm);
    border-bottom: 1px solid var(--border-color);
  }
  
  .userTableCell {
    display: flex;
    padding: 0.5rem 0;
    border-bottom: 1px dashed var(--border-color);
    white-space: normal;
  }
  
  .userTableCell:last-child {
    border-bottom: none;
  }
  
  .userTableCell::before {
    content: attr(data-label);
    width: 40%;
    font-weight: 600;
    color: var(--text-medium);
  }
  
  .pagination {
    justify-content: center;
  }

  .pageHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .pageTitle {
    font-size: 1.5rem;
  }

  .actionButtons {
    flex-direction: column;
    width: 100%;
  }

  .actionButtons button {
    width: 100%;
  }
}

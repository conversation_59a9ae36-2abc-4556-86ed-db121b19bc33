/* 管理後台共用樣式 */
@import './admin-variables.css';

/* 頁面容器 */
.adminPageContainer {
  width: 100%;
  padding: 24px;
}

/* 頁面標題區域 */
.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  margin-bottom: 0.75rem;
}

.pageTitle {
  font-size: 1.6rem;
  font-weight: 700;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 按鈕樣式 */
.addButton {
  padding: var(--space-sm) var(--space-md);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: var(--shadow-sm);
}

.addButton:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.addButton:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* 篩選區域 */
.filterContainer {
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.filterHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.filterTitle {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-dark);
}

.filterRow {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-md);
  align-items: flex-end;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  flex: 1;
  min-width: 200px;
}

.filterLabel {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-medium);
}

.filterInput {
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.95rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.filterInput:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
  outline: none;
}

.filterButton {
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: transparent;
  color: var(--text-medium);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
}

.filterButton:hover {
  background-color: var(--bg-medium);
  border-color: var(--border-color-dark);
  color: var(--text-dark);
}

.filterButtonPrimary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.filterButtonPrimary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  color: white;
}

/* 表格樣式 */
.dataTable {
  width: 100%;
  border-collapse: collapse;
}

.tableHeader {
  padding: var(--space-md);
  text-align: left;
  font-weight: 600;
  color: var(--text-dark);
  border-bottom: 2px solid var(--border-color);
  background-color: var(--bg-light);
}

.tableCell {
  padding: var(--space-md);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.95rem;
  color: var(--text-dark);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tableRow {
  transition: background-color 0.2s;
}

.tableRow:hover {
  background-color: var(--primary-light);
}

.tableRow:last-child .tableCell {
  border-bottom: none;
}

/* 分頁控制 */
.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: var(--space-lg);
  gap: var(--space-sm);
}

.pageButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: transparent;
  color: var(--text-medium);
  cursor: pointer;
  transition: all 0.2s;
}

.pageButton:hover {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.pageButtonActive {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pageButtonDisabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageButtonDisabled:hover {
  background-color: transparent;
  color: var(--text-medium);
  border-color: var(--border-color);
}

/* 操作按鈕 */
.actionButton {
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  background-color: transparent;
  color: var(--text-medium);
  cursor: pointer;
  transition: all 0.2s;
  margin-right: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.viewButton:hover {
  background-color: var(--info-light);
  color: var(--info-color);
  border-color: var(--info-color);
}

.editButton:hover {
  background-color: var(--warning-light);
  color: var(--warning-color);
  border-color: var(--warning-color);
}

.actionButtonDanger {
  color: var(--danger-color);
  border-color: var(--danger-color);
}

.actionButtonDanger:hover {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .filterRow {
    flex-direction: column;
  }
  
  .filterGroup {
    width: 100%;
  }
}

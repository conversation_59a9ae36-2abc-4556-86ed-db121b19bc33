import React, { useState } from 'react';
import { useOrderManagement } from '../../../hooks/useOrderManagement';
import { OrderStatus } from '../../../types/order';
import { OrderTable } from '../../../components/admin/OrderTable';
import { ErrorBoundary } from '../../../components/common/ErrorBoundary';
import styles from './styles.module.css';

/**
 * 訂單管理頁面內容元件
 */
const OrdersPageContent: React.FC = () => {
  const {
    orders,
    loading,
    filter,
    pagination,
    updateFilter,
    resetFilter,
    updatePage,
    batchUpdateStatus,
    batchDelete,
    reload,
    updateManualDiscount,
  } = useOrderManagement();

  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  const toggleSelect = (id: string) => {
    setSelectedIds(prev => (prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]));
  };

  const selectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(orders.map(o => o.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSaveOrder = async (updated: Partial<import('../../../types/order').Order>) => {
    if (!updated.id) return;
    try {
      // TODO: 取得實際登入管理員ID
      const adminId = localStorage.getItem('adminUserId');
      await updateManualDiscount(
        updated.id,
        updated.manualDiscountAmount ?? 0,
        adminId,
        updated.adminNotes ?? ''
      );
      await reload();
    } catch (error) {
      console.error('更新手動折扣失敗', error);
    }
  };

  return (
    <div className={styles.container}>
      <header className={styles.header}>
        <h1 className={styles.title}>訂單管理</h1>
        <div className={styles.actions}>
          <button className={styles.resetButton} onClick={resetFilter} disabled={loading}>
            重置篩選
          </button>
        </div>
      </header>

      <div className={styles.contentCard}>
        <div className={styles.filters}>
          <div className={styles.filterRow}>
            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>訂單狀態：</label>
              <select
                className={styles.filterSelect}
                value={filter.status || ''}
                onChange={e =>
                  updateFilter({ status: (e.target.value as OrderStatus) || undefined })
                }
                disabled={loading}
              >
                <option value="">全部</option>
                <option value={OrderStatus.Pending}>待處理</option>
                <option value={OrderStatus.Processing}>處理中</option>
                <option value={OrderStatus.Completed}>已完成</option>
                <option value={OrderStatus.Cancelled}>已取消</option>
              </select>
            </div>

            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>專案：</label>
              <select
                className={styles.filterSelect}
                value={filter.projectId || ''}
                onChange={e => updateFilter({ projectId: e.target.value || undefined })}
                disabled={loading}
              >
                <option value="">全部專案</option>
                <option value="project-1">有機蔬果預購</option>
                <option value="project-2">天然手工皂團購</option>
                <option value="project-3">環保餐具組預購</option>
              </select>
            </div>

            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>訂單編號：</label>
              <input
                type="text"
                className={styles.filterInput}
                value={filter.orderId || ''}
                onChange={e => updateFilter({ orderId: e.target.value || undefined })}
                placeholder="輸入訂單編號"
                disabled={loading}
              />
            </div>

            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>用戶名稱：</label>
              <input
                type="text"
                className={styles.filterInput}
                value={filter.userName || ''}
                onChange={e => updateFilter({ userName: e.target.value || undefined })}
                placeholder="輸入用戶名稱"
                disabled={loading}
              />
            </div>
          </div>

          <div className={styles.filterRow}>
            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>起始日期：</label>
              <input
                type="date"
                className={styles.filterInput}
                value={filter.dateStart || ''}
                onChange={e => updateFilter({ dateStart: e.target.value || undefined })}
                disabled={loading}
              />
            </div>

            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>結束日期：</label>
              <input
                type="date"
                className={styles.filterInput}
                value={filter.dateEnd || ''}
                onChange={e => updateFilter({ dateEnd: e.target.value || undefined })}
                disabled={loading}
              />
            </div>

            <div className={styles.filterGroup} style={{ flex: 1 }}>
              <label className={styles.filterLabel}>關鍵字搜尋：</label>
              <input
                type="text"
                className={styles.filterInput}
                value={filter.searchQuery || ''}
                onChange={e => updateFilter({ searchQuery: e.target.value })}
                placeholder="搜尋訂單編號、用戶名稱或郵箱"
                disabled={loading}
              />
            </div>
          </div>
        </div>

        <div className={styles.batchActions}>
          <button
            className={styles.batchButton}
            disabled={selectedIds.length === 0}
            onClick={async () => {
              if (selectedIds.length === 0) return;
              const status = window.prompt(
                '請輸入目標狀態 (Pending, Confirmed, Cancelled, Completed)'
              );
              if (!status) return;
              await batchUpdateStatus(selectedIds, status as OrderStatus);
              await reload();
              setSelectedIds([]);
            }}
          >
            批次更新狀態
          </button>
          <button
            className={`${styles.batchButton} ${styles.danger}`}
            disabled={selectedIds.length === 0}
            onClick={async () => {
              if (selectedIds.length === 0) return;
              const confirmed = window.confirm(
                `確定要刪除 ${selectedIds.length} 筆訂單嗎？此操作不可恢復！`
              );
              if (!confirmed) return;
              await batchDelete(selectedIds);
              await reload();
              setSelectedIds([]);
            }}
          >
            批次刪除
          </button>
          <span className={styles.selectedCount}>已選取 {selectedIds.length} 筆</span>
        </div>

        <OrderTable
          orders={orders}
          loading={loading}
          className={styles.table}
          selectedIds={selectedIds}
          onToggleSelect={toggleSelect}
          onSelectAll={selectAll}
          onSaveOrder={handleSaveOrder}
        />
      </div>
      <footer className={styles.footer}>
        <div className={styles.pagination}>
          <button
            className={styles.pageButton}
            onClick={() => updatePage(pagination.current - 1)}
            disabled={loading || pagination.current <= 1}
          >
            上一頁
          </button>
          <span className={styles.pageInfo}>
            第 {pagination.current} 頁，共 {Math.ceil(pagination.total / pagination.pageSize)} 頁
          </span>
          <button
            className={styles.pageButton}
            onClick={() => updatePage(pagination.current + 1)}
            disabled={
              loading || pagination.current >= Math.ceil(pagination.total / pagination.pageSize)
            }
          >
            下一頁
          </button>
        </div>
      </footer>
    </div>
  );
};

/**
 * 訂單管理頁面
 */
const OrdersPage: React.FC = () => {
  return (
    <ErrorBoundary>
      <OrdersPageContent />
    </ErrorBoundary>
  );
};

export default OrdersPage;

import React, { ReactNode } from 'react';
import styles from './styles.module.css';

/**
 * 卡片元件屬性介面
 * 定義卡片元件的所有可配置屬性
 */
interface CardProps {
  /**
   * 卡片標題
   */
  title?: string;
  /**
   * 卡片子標題
   */
  subtitle?: string;
  /**
   * 卡片圖片 URL
   */
  imageUrl?: string;
  /**
   * 卡片圖片替代文字
   * @default '卡片圖片'
   */
  imageAlt?: string;
  /**
   * 卡片狀態標籤
   * 用於顯示卡片的當前狀態，如待處理、進行中、已完成等
   */
  status?: {
    /**
     * 標籤文字
     */
    text: string;
    /**
     * 標籤類型
     * - pending: 待處理（黃色）
     * - active: 進行中（綠色）
     * - completed: 已完成（藍色）
     * - custom: 自定義顏色
     */
    type: 'pending' | 'active' | 'completed' | 'custom';
    /**
     * 自定義標籤顏色（僅在 type 為 'custom' 時使用）
     */
    color?: string;
  };
  /**
   * 卡片點擊事件處理函數
   * 當設置此屬性時，卡片將變為可點擊狀態
   */
  onClick?: () => void;
  /**
   * 卡片底部操作區域
   * 通常用於放置按鈕等操作元素
   */
  actions?: ReactNode;
  /**
   * 卡片內容
   * 卡片的主要內容區域
   */
  children?: ReactNode;
  /**
   * 卡片額外的 CSS 類名
   * 用於自定義卡片樣式
   */
  className?: string;
  /**
   * 是否顯示卡片懸停效果
   * 當設置為 true 時，懸停時卡片會有輕微上浮和陰影增強效果
   * @default true
   */
  hoverEffect?: boolean;
}

/**
 * 卡片元件
 *
 * 用於顯示專案、商品等內容的容器元件，支持圖片、標題、內容和操作區域。
 * 可以設置懸停效果和點擊事件，適用於各種卡片式佈局。
 *
 * 使用示例:
 * ```tsx
 * <Card
 *   title="商品名稱"
 *   subtitle="商品描述"
 *   imageUrl="/path/to/image.jpg"
 *   status={{ text: "熱賣中", type: "active" }}
 *   onClick={handleCardClick}
 * >
 *   <p>商品詳細內容...</p>
 * </Card>
 * ```
 *
 * @param props - 卡片元件屬性
 * @returns 卡片元件
 */
const Card: React.FC<CardProps> = ({
  title,
  subtitle,
  imageUrl,
  imageAlt = '卡片圖片',
  status,
  onClick,
  actions,
  children,
  className = '',
  hoverEffect = true,
}) => {
  // 根據狀態類型獲取對應的 CSS 類名
  const getStatusClassName = (type: string): string => {
    switch (type) {
      case 'pending':
        return styles.statusPending;
      case 'active':
        return styles.statusActive;
      case 'completed':
        return styles.statusCompleted;
      default:
        return '';
    }
  };

  return (
    <div
      className={[styles.card, ...(hoverEffect ? [styles.hover] : []), className]
        .filter(Boolean)
        .join(' ')}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
    >
      {imageUrl && (
        <div className={styles.imageContainer}>
          <img src={imageUrl} alt={imageAlt} className={styles.image} />
          {status && (
            <div
              className={`${styles.status} ${getStatusClassName(status.type)}`}
              style={
                status.type === 'custom' && status.color
                  ? { backgroundColor: status.color }
                  : undefined
              }
            >
              {status.text}
            </div>
          )}
        </div>
      )}

      <div className={styles.content}>
        {(title || subtitle) && (
          <div className={styles.header}>
            {title && <h3 className={styles.title}>{title}</h3>}
            {subtitle && <div className={styles.subtitle}>{subtitle}</div>}
            {status && !imageUrl && (
              <div
                className={`${styles.status} ${getStatusClassName(status.type)}`}
                style={
                  status.type === 'custom' && status.color
                    ? { backgroundColor: status.color }
                    : undefined
                }
              >
                {status.text}
              </div>
            )}
          </div>
        )}

        {children && <div className={styles.body}>{children}</div>}

        {actions && <div className={styles.actions}>{actions}</div>}
      </div>
    </div>
  );
};

export default Card;

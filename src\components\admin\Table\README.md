# Table 組件

## 簡介
Table 組件是一個靈活的表格組件，用於在管理後台中顯示和管理數據。支援排序、分頁和自定義列渲染。

## 功能特點
- 支援自定義列配置
- 內建分頁功能
- 支援排序功能
- 響應式設計
- 自定義渲染函數
- 支援行操作按鈕

## 使用方法

### 基本用法
```tsx
import { Table } from '@/components/admin/Table';

const columns = [
  {
    key: 'id',
    title: 'ID',
    width: 80,
  },
  {
    key: 'name',
    title: '名稱',
    width: 200,
  },
];

const MyComponent = () => {
  return (
    <Table
      columns={columns}
      data={data}
      loading={false}
      pagination={{
        current: 1,
        pageSize: 10,
        total: 100,
      }}
      onChange={handleTableChange}
    />
  );
};
```

### 自定義列渲染
```tsx
const columns = [
  {
    key: 'status',
    title: '狀態',
    render: (value: string) => (
      <Badge type={value === 'active' ? 'success' : 'error'}>
        {value}
      </Badge>
    ),
  },
];
```

## Props 定義

| 屬性 | 類型 | 必填 | 預設值 | 說明 |
|------|------|------|--------|------|
| columns | Column[] | 是 | - | 表格列配置 |
| data | any[] | 是 | - | 表格數據 |
| loading | boolean | 否 | false | 載入狀態 |
| pagination | PaginationProps | 否 | - | 分頁配置 |
| onChange | Function | 否 | - | 表格變更回調 |
| rowKey | string | 否 | 'id' | 行唯一標識 |

### Column 類型定義
```typescript
interface Column {
  key: string;
  title: string;
  width?: number;
  render?: (value: any, record: any) => React.ReactNode;
  sorter?: boolean;
}
```

### PaginationProps 類型定義
```typescript
interface PaginationProps {
  current: number;
  pageSize: number;
  total: number;
}
```

## 樣式定製
組件使用 CSS Modules 進行樣式隔離，可以通過以下變數進行樣式定製：

```css
/* styles.module.css */
.table {
  --admin-table-header-bg: var(--admin-gray-100);
  --admin-table-border-color: var(--admin-gray-200);
  --admin-table-hover-bg: var(--admin-gray-50);
}
```

## 最佳實踐
1. 始終提供 `rowKey` 屬性以確保表格行的唯一性
2. 使用 `render` 函數進行複雜數據的格式化
3. 合理設置 `pageSize` 以優化性能
4. 使用 `loading` 狀態提供更好的用戶體驗

## 注意事項
1. 大數據量時建議使用分頁
2. 自定義渲染函數應注意性能優化
3. 響應式設計時注意表格列的優先級 
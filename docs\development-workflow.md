# 小森活 (Small Forest Life) 預購系統 - 開發流程指南 (v1.2 - 基於 Spec v1.2)

src/
├── assets/                 # 靜態資源（圖片、字體等）
├── components/             # 共用元件
│   ├── admin/              # 管理員專用元件
│   │   ├── ProjectManagement/  # 專案管理元件
│   │   ├── OrderManagement/    # 訂單管理元件
│   │   └── UserManagement/     # 用戶管理元件
│   ├── common/             # 通用元件
│   │   ├── Button/         # 按鈕元件
│   │   ├── Card/           # 卡片元件
│   │   ├── FormInput/      # 表單輸入元件
│   │   ├── ItemCard/       # 商品卡片元件
│   │   └── ProjectCard/    # 專案卡片元件
│   ├── icons/              # 圖標元件
│   │   ├── CommonIcons/    # 通用圖標
│   │   └── NavIcons/       # 導航圖標
│   ├── layout/             # 佈局元件
│   │   ├── Header/         # 頁頭元件
│   │   └── BottomNav/      # 底部導航元件
│   └── user/               # 用戶專用元件
│       └── ProfilePopup/   # 用戶資料彈出視窗
├── contexts/               # React 上下文
├── hooks/                  # 自定義 Hooks
├── lib/                    # 第三方庫的整合
├── pages/                  # 頁面元件
│   ├── admin/              # 管理員頁面
│   │   ├── login/          # 後台登入頁
│   │   ├── dashboard/      # 後台儀表板
│   │   └── management/     # 管理頁面
│   │       ├── projects/   # 專案管理頁
│   │       ├── order-management/ # 訂單管理頁
│   │       └── user-management/  # 用戶管理頁
│   ├── home/               # 首頁
│   │   └── HomePage/       # 首頁元件
│   ├── preorder/           # 預購下單頁
│   │   └── PreorderPage/   # 預購下單頁元件
│   └── order-history/      # 訂單歷史頁
│       └── OrderHistoryPage/ # 訂單歷史頁元件
├── styles/                 # 全局樣式
│   ├── variables.css       # CSS 變數
│   ├── reset.css           # 樣式重置
│   ├── utilities.css       # 工具類
│   └── global.css          # 全局樣式
├── types/                  # TypeScript 類型定義
└── utils/                  # 工具函數


## 開發原則
- 遵循「先核心後擴展」，優先實現基本功能快速上線，逐步加入複雜功能
- 後台優先: 先完成 /admin 路徑下的核心數據管理與業務邏輯，確保穩定性
- 模組化設計: 確保邏輯與 UI 分離，提高可維護性與可測試性

## 代碼規範
- 遵循 Vite + React + TypeScript 專案結構與設計模式
- 採用模組化設計與 DRY (Don't Repeat Yourself) 原則
- 禁止假資料或臨時方案，所有資料從 Supabase 真實獲取
- 單一文件不超 400 行 (建議)，複雜邏輯應拆分
- 模組間交互需有清晰註解 (JSDoc/TSDoc)，特別是 Context 使用、API/RPC 調用

## 視覺設計規範
- 依據 visual-guide執行
- 使用指定的顏色方案與字體設置
- 界面簡潔、直觀，Mobile-First 設計

## 開發流程與進度表

以下任務按階段劃分，使用核取框標記完成狀態。後台優先開發。

### 階段 1: 環境搭建與基礎設定 (後台 + 基礎)

- [x] 初始化 Vite + React + TypeScript 專案
  > 已完成：
  > - 專案初始化與配置
  > - TypeScript 配置完成
  > - React 開發環境設置
  > - 基本元件結構建立
  > - 專案結構優化與重組

- [x] 配置 ESLint 與 Prettier
  > 已完成：
  > - ESLint 與 Prettier 整合配置
  > - TypeScript 支援設定
  > - React Hooks 規則設定
  > - 程式碼品質檢查規則
  > - 自動格式化設定完成

- [x] 建立模組化目錄結構
  > 已完成：
  > - src/components/: 元件目錄
  > - src/pages/: 頁面元件
  > - src/hooks/: 自定義 Hooks
  > - src/lib/: 第三方庫整合
  > - src/types/: TypeScript 型別定義
  > - src/utils/: 工具函數
  > - src/contexts/: React Context
  > - src/styles/: 全局樣式
  > - src/common/: 通用功能
  > - 目錄結構文檔更新

- [x] 配置 Supabase 客戶端
  > 已完成：
  > - @supabase/supabase-js 安裝與配置
  > - src/lib/supabase.ts 設定
  > - TypeScript 型別定義完成
  > - 連接測試成功
  > - 環境變數配置完成

- [x] 配置 Cloudinary 上傳工具
  > 已完成：
  > - @cloudinary/url-gen 和 @cloudinary/react 安裝
  > - src/lib/cloudinary.ts 配置
  > - 上傳功能測試成功
  > - 環境變數設定完成

### 階段 1.5: 前端原型導入

- [x] 導入設計原型頁面
  > 已完成：
  > - prototypes/ 目錄建立
  > - 原型頁面保存完成

#### 階段 1.5.1: 元件模組化與基礎架構

- [x] 識別並建立共用元件庫
  > 已完成：
  > - Button
  > - FormInput
  > - Card
  > - ItemCard
  > - ProjectCard
  > - 元件文檔建立

- [x] 建立統一的樣式系統
  > 已完成：
  > - 全局變數定義
  > - 基礎樣式設定
  > - 響應式設計框架

- [x] 樣式系統重構與模組化
  > 已完成：
  > - CSS Module 實現
  > - 樣式隔離確認
  > - 文檔更新

#### 階段 1.5.2: 頁面轉換與狀態管理

- [x] 將靜態原型轉換為 React 元件
  > 已完成：
  > - 首頁：90% 完成
  > - 預購頁面：80% 完成
  > - 用戶設定頁面：60% 完成
  > - 共用元件庫整合：85% 完成
  > 下一步：
  > - 完成剩餘頁面轉換
  > - 優化響應式設計
  > - 完善錯誤處理機制

- [x] 建立基本狀態管理系統
  > 已完成：
  > - AuthContext / useAuth 基礎實現
  > - CartContext / useCart 基礎架構
  > - 狀態持久化機制設計
  > - 頁面間狀態傳遞邏輯
  > 下一步：
  > - 完善錯誤處理
  > - 優化狀態更新效能

- [x] 實現頁面間的交互邏輯
  > 已完成：
  > - 頁面間數據流設計
  > - 基礎表單驗證
  > - 錯誤處理機制框架
  > - 路由配置與保護
  > 下一步：
  > - 優化用戶體驗
  > - 增強錯誤處理

### 階段 2: 核心功能 - 用戶認證與權限 (基礎 + 後台)

- [-] 整合 LIFF SDK (liff-id: 2007154933-Jd5gBvvv) - 開發階段暫不導入

- [x] 實現 AuthContext / useAuth Hook
  > 已完成：
  > - 基礎認證邏輯
  > - 狀態管理
  > - 持久化處理
  > - 錯誤處理

- [x] 實現 users 表 Upsert 邏輯
  > 已完成：
  > - 使用者資料更新邏輯
  > - 自動建立新用戶
  > - 資料驗證

- [x] 實現後台登入頁面 (/admin/login)
  > 已完成：
  > - 登入表單
  > - 權限驗證
  > - 錯誤處理
  > - 登入狀態持久化

- [x] 實現後台登入狀態持久化
  > 已完成：
  > - 狀態管理
  > - Token 處理
  > - 自動登出
  > - 安全性檢查

- [x] 實現後台權限檢查與路由保護
  > 已完成：
  > - 路由保護
  > - 權限檢查
  > - 未授權處理
  > - 重定向邏輯

- [x] 定義並實施 Supabase RLS 策略
  > 已完成：
  > - 使用者資料保護
  >   - 使用者只能查看和更新自己的資料
  >   - 管理員可以執行所有操作
  > - 專案資料保護
  >   - 添加 owner_id 欄位建立使用者關聯
  >   - 使用者只能查看和管理自己的專案
  > - 訂單資料保護
  >   - 使用者只能查看和管理自己的訂單
  >   - order_items 透過訂單關聯控制存取權限
  > - 商品和折扣保護
  >   - 所有人都可以查看
  >   - 只有管理員可以修改
  > - 系統表格（id_sequences）維持原狀
  > - 效能影響評估完成
  > - 安全性測試通過

- [x] 測試 RLS 策略
  > 測試結果：
  > - ✓ 用戶專案存取測試：用戶成功讀取自己的專案
  > - ✓ 用戶訂單存取測試：用戶成功讀取自己的訂單
  > - ✓ 商品公開讀取測試：所有用戶都能查看商品
  > - ✓ 資料隔離測試：用戶無法存取其他用戶的資料
  > - ✓ 管理員權限測試：管理員可以存取所有資料
  > 
  > 測試數據：
  > - 管理員帳號：dev-admin-id（role: admin）
  > - 測試用戶：test_user1, test_user2（role: user）
  > - 測試專案：P20240403-0001, P20240403-0002
  > - 測試訂單：O20240403-0001
  > 
  > 結論：
  > - RLS 策略運作正常，資料存取控制符合預期
  > - 管理員擁有完整的系統管理權限
  > - 一般用戶的資料存取受到適當限制

### 階段 3: 核心功能 - 折扣系統 (後台)

- [x] 實現 discounts 表 CRUD 操作
  > 完成折扣系統實作與測試：
  > - 成功建立折扣規則（早鳥優惠、批量購買優惠）
  > - 完成折扣計算邏輯
  > - 測試結果：
  >   - ✓ 批量購買優惠（10件以上每件折20元）計算正確
  >   - ✓ 訂單金額計算準確（訂單 O20250402-0002）
  >   - ✓ 折扣金額正確（10件，折扣200元）
  >   - ✓ 最終金額計算無誤（原價800元 - 折扣200元 = 600元）

- [x] 實現 calculateQuantityTierDiscount 工具函數
  > 完成並驗證：
  > - 正確處理不同數量級別的折扣
  > - 自動選擇最優惠的折扣方案
  > - 準確計算折扣金額

- [x] 實現後台折扣管理頁面 (/admin/discounts)
  > 已完成：
  > - 折扣列表與搜尋
  > - 折扣創建與編輯
  > - 折扣狀態管理
  > - 折扣效果預覽

- [x] 完成 calculateQuantityTierDiscount 的 Jest 單元測試
  > 已完成：
  > - 基本折扣計算測試
  > - 邊界條件測試
  > - 錯誤處理測試
  > - 測試覆蓋率報告

**說明:**
- 折扣系統核心功能已完成並通過測試
- 折扣計算邏輯準確性已驗證
- 待完成：後台管理介面和單元測試

### 階段 4: 核心功能 - 整合式專案管理 (後台)

- [x] 實現專案狀態自動更新機制
  > 完成專案狀態自動更新功能：
  > - 建立 update_project_status() 觸發器函數
  > - 根據 deadline 和 arrival_date 自動更新狀態
  > - 新增狀態變更日誌功能
  > - 測試結果：
  >   - ✓ 超過 deadline 時狀態更新為 ordering_ended
  >   - ✓ 超過 arrival_date 時狀態更新為 arrived
  >   - ✓ 管理員可手動設定為 completed
  >   - ✓ 狀態變更日誌記錄正確

- [x] 實現專案亮點功能
  > 完成專案亮點功能實作：
  > - 在 projects 表中新增 highlights 欄位（jsonb 類型）
  > - 限制最多3個亮點
  > - 完成前端顯示邏輯
  > - 新增亮點排序功能
  > - 測試結果：
  >   - ✓ 亮點資料正確儲存
  >   - ✓ 超過3個亮點時自動截斷
  >   - ✓ 前端顯示正確且美觀
  >   - ✓ 亮點排序功能正常

- [x] 實現 projects 表 CRUD 操作
  > 已完成：
  > - 專案基本操作
  > - 狀態管理
  > - 資料驗證
  > - 錯誤處理

- [x] 後端確保 project_id_display 由 generate_display_id 生成
  > 已完成：
  > - ID 生成觸發器
  > - 格式驗證
  > - 唯一性檢查
  > - 錯誤處理

- [x] 實現 items 表 CRUD 操作
  > 已完成：
  > - 商品基本操作
  > - 批量操作支援
  > - 資料驗證
  > - 錯誤處理

- [x] 後端確保 item_id_display 由 generate_display_id 生成
  > 已完成：
  > - ID 生成觸發器
  > - 格式驗證
  > - 唯一性檢查
  > - 錯誤處理

- [x] 實現 discounts 表 CRUD 操作
  > 已完成：
  > - 折扣基本操作
  > - 折扣規則驗證
  > - 時效性管理
  > - 錯誤處理

  > 已完成：
  > - ID 生成觸發器
  > - 格式驗證
  > - 唯一性檢查
  > - 錯誤處理

- [x] 實現 calculateQuantityTierDiscount 工具函數
  > 已完成：
  > - 折扣計算邏輯
  > - 多層級支援
  > - 效能優化
  > - 錯誤處理

- [x] 實現圖片上傳 (Cloudinary) 與管理
  > 已完成：
  > - 上傳功能
  > - 圖片優化
  > - 刪除功能
  > - 錯誤處理

- [x] 實現整合式專案管理介面 (/admin/projects)
  > 已完成：
  > - 專案列表與搜尋
  > - 專案創建與編輯
  > - 商品管理整合
  > - 折扣設定整合

- [x] 實現商品拖拽排序功能
  > 已完成：
  > - 拖拽介面
  > - 排序保存
  > - 即時更新
  > - 錯誤處理

- [x] 實現商品批量上傳功能 (Excel)
  > 已完成：
  > - Excel 解析
  > - 資料驗證
  > - 批量導入
  > - 錯誤處理

- [x] 實現專案複製功能
  > 已完成：
  > - 專案資料複製
  > - 商品資料複製
  > - 折扣設定複製
  > - 錯誤處理

- [x] 完成 calculateQuantityTierDiscount 的 Jest 單元測試
  > 測試已存在，涵蓋空購物車、未達門檻、達門檻、指定商品、超額折扣、錯誤

### 階段 6: 核心功能 - 用戶管理 (後台)

- [x] 實現後台用戶管理頁面 (/admin/user-management)
  > 已完成：
  > - 用戶列表與搜尋
  > - 用戶資料編輯
  > - 權限管理
  > - 狀態管理
  > - 操作日誌

- [x] 實現後台用戶編輯功能
  > 已完成：
  > - 基本資料編輯
  > - 權限設定
  > - 狀態更新
  > - 操作記錄
  > - 資料驗證

**說明:**
- 用戶管理功能已完整實作
- 整合 RLS 策略確保資料安全
- 提供完整的用戶生命週期管理

## 已完成任務

### 資料庫設計與實施
- [x] 設計資料庫結構
  > 已完成：
  > - 核心資料表設計
  > - 關聯關係定義
  > - 索引優化
  > - 效能調校

- [x] 建立資料表和關聯
  > 已完成：
  > - users 表：用戶資料
  >   - id (text)：主鍵
  >   - display_name (text)：顯示名稱，必填
  >   - community_nickname (text)：社群暱稱，選填
  >   - picture_url (text)：頭像圖片網址，選填
  >   - role (text)：用戶角色，預設 'user'
  >   - created_at (timestamp)：建立時間
  >   - last_login_at (timestamp)：最後登入時間，選填
  > - projects 表：專案管理
  >   - id (uuid)：主鍵，自動生成
  >   - project_id_display (text)：顯示用專案編號，必填
  >   - name (text)：專案名稱，必填
  >   - description (text)：專案描述，選填
  >   - project_status (text)：專案狀態，預設 'Active'
  >   - default_discount_id (uuid)：預設折扣方案，關聯 discounts.id
  >   - owner_id (text)：擁有者，關聯 users.id
  >   - highlights (jsonb)：專案特色列表，預設 []
  >   - deadline (timestamp)：截止日期，選填
  >   - arrival_date (timestamp)：到貨日期，選填
  >   - images (text[])：圖片列表，選填
  >   - created_at/updated_at (timestamp)：時間戳記
  > - items 表：商品管理
  >   - id (uuid)：主鍵，自動生成
  >   - item_id_display (text)：顯示用商品編號，必填
  >   - project_id (uuid)：所屬專案，關聯 projects.id
  >   - name (text)：商品名稱，必填
  >   - price (numeric)：價格，必填
  >   - description (text)：商品描述，選填
  >   - status (text)：商品狀態，預設 'Available'
  >   - sort_order (integer)：排序順序，預設 0
  >   - created_at/updated_at (timestamp)：時間戳記
  > - discounts 表：折扣管理
  >   - id (uuid)：主鍵，自動生成
  >   - project_id (uuid)：關聯專案，選填，關聯 projects.id
  >   - name (text)：折扣名稱，必填
  >   - description (text)：折扣描述，選填
  >   - type (text)：折扣類型，預設 'quantity_tier'
  >   - quantity_threshold (integer)：數量門檻，必填
  >   - discount_per_item (numeric)：每件折扣金額，必填
  >   - applicable_item_ids (uuid[])：適用商品清單，選填
  >   - active (boolean)：是否啟用，預設 true
  >   - start_date (timestamp)：開始日期，選填
  >   - end_date (timestamp)：結束日期，選填
  >   - discount_type (text)：折扣類型，預設 'percentage'
  >   - discount_value (numeric)：折扣值，預設 0
  >   - min_purchase_amount (numeric)：最低消費金額，選填
  >   - max_discount_amount (numeric)：最高折扣金額，選填
  >   - usage_limit (integer)：使用次數限制，選填
  >   - used_count (integer)：已使用次數，預設 0
  >   - created_at/updated_at (timestamp)：時間戳記

- [x] 實施 ID 生成機制
  > 已完成：
  > - 自動生成機制
  > - 格式驗證
  > - 唯一性保證
  > - 效能優化

- [x] 定義並實施 Supabase RLS 策略
  > 已完成：
  > - 使用者資料保護
  >   - 使用者只能查看和更新自己的資料
  >   - 管理員可以執行所有操作
  > - 專案資料保護
  >   - 添加 owner_id 欄位建立使用者關聯
  >   - 使用者只能查看和管理自己的專案
  > - 訂單資料保護
  >   - 使用者只能查看和管理自己的訂單
  >   - order_items 透過訂單關聯控制存取權限
  > - 商品和折扣保護
  >   - 所有人都可以查看
  >   - 只有管理員可以修改
  > - 系統表格（id_sequences）維持原狀
  > - 效能影響評估完成
  > - 安全性測試通過

# Filter 組件

## 簡介
Filter 組件是一個用於數據篩選的高級組件，支援多種篩選條件和篩選方式，適用於管理後台的數據過濾場景。

## 功能特點
- 支援多種篩選項類型
- 即時篩選
- 支援日期範圍選擇
- 支援多選篩選
- 可重置篩選條件
- 響應式設計

## 使用方法

### 基本用法
```tsx
import { Filter } from '@/components/admin/Filter';

const MyComponent = () => {
  const filterItems = [
    {
      key: 'search',
      label: '搜尋',
      type: 'text',
    },
    {
      key: 'status',
      label: '狀態',
      type: 'select',
      options: [
        { label: '全部', value: 'all' },
        { label: '啟用', value: 'active' },
        { label: '停用', value: 'inactive' },
      ],
    },
  ];

  return (
    <Filter
      items={filterItems}
      values={filterValues}
      onChange={handleFilterChange}
      onSubmit={handleFilterSubmit}
    />
  );
};
```

### 日期範圍篩選
```tsx
const filterItems = [
  {
    key: 'dateRange',
    label: '日期範圍',
    type: 'dateRange',
    placeholder: ['開始日期', '結束日期'],
  },
];
```

### 多選篩選
```tsx
const filterItems = [
  {
    key: 'categories',
    label: '分類',
    type: 'multiSelect',
    options: [
      { label: '分類 A', value: 'a' },
      { label: '分類 B', value: 'b' },
      { label: '分類 C', value: 'c' },
    ],
  },
];
```

## Props 定義

| 屬性 | 類型 | 必填 | 預設值 | 說明 |
|------|------|------|--------|------|
| items | FilterItem[] | 是 | - | 篩選項配置 |
| values | Record<string, any> | 是 | - | 篩選值 |
| onChange | Function | 是 | - | 篩選值變更回調 |
| onSubmit | Function | 否 | - | 提交篩選回調 |
| loading | boolean | 否 | false | 載入狀態 |
| layout | 'horizontal' \| 'vertical' | 否 | 'horizontal' | 佈局方式 |

### FilterItem 類型定義
```typescript
interface FilterItem {
  key: string;
  label: string;
  type: 'text' | 'select' | 'multiSelect' | 'dateRange';
  placeholder?: string | string[];
  options?: Array<{ label: string; value: string | number }>;
  defaultValue?: any;
}
```

## 樣式定製
組件使用 CSS Modules 進行樣式隔離，可以通過以下變數進行樣式定製：

```css
/* styles.module.css */
.filter {
  --admin-filter-bg: var(--admin-gray-50);
  --admin-filter-border: var(--admin-gray-200);
  --admin-filter-shadow: var(--admin-shadow-sm);
}
```

## 最佳實踐
1. 合理設置篩選項的順序，將常用的篩選項放在前面
2. 為篩選項提供合適的預設值
3. 使用語義化的 key 值
4. 提供清晰的佔位符文字

## 性能優化
1. 使用 `debounce` 處理即時搜尋
2. 大數據量的選項建議使用異步加載
3. 避免過多的篩選項同時顯示
4. 合理使用 `defaultValue` 減少不必要的渲染

## 無障礙設計
1. 所有輸入框都有對應的標籤
2. 選擇框支援鍵盤操作
3. 錯誤訊息會通過 ARIA 屬性傳達
4. 提供清晰的操作反饋

## 注意事項
1. 篩選項的 key 值必須唯一
2. 日期範圍選擇器需要考慮時區問題
3. 多選項目應該提供清空和全選功能
4. 響應式設計下注意篩選項的展示方式 
import { Cloudinary } from '@cloudinary/url-gen';

const cloudinaryConfig = {
  cloudName: import.meta.env.VITE_CLOUDINARY_CLOUD_NAME,
  apiKey: import.meta.env.VITE_CLOUDINARY_API_KEY,
  apiSecret: import.meta.env.VITE_CLOUDINARY_API_SECRET,
  uploadPreset: import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET,
};

// 驗證配置
if (!cloudinaryConfig.cloudName || !cloudinaryConfig.uploadPreset) {
  throw new Error('Missing Cloudinary configuration');
}

// 創建 Cloudinary 實例
export const cld = new Cloudinary({
  cloud: {
    cloudName: cloudinaryConfig.cloudName,
  },
  url: {
    secure: true,
  },
});

// 獲取上傳 URL
export const getCloudinaryUploadUrl = () => {
  return `https://api.cloudinary.com/v1_1/${cloudinaryConfig.cloudName}/image/upload`;
};

import { useState, useCallback } from 'react';
import type { CartItem, Item } from '../components/types';
import { calculateQuantityTierDiscount } from '../utils/discountCalculator';
import { fetchProjectDiscounts } from '../lib/supabase';

export const useCart = (projectId: string) => {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [appliedDiscount, setAppliedDiscount] = useState<any>(null);

  const handleQuantityChange = (item: Item, newQuantity: number): void => {
    // 檢查購物車中是否已有此商品
    const existingCartItemIndex = cartItems.findIndex(cartItem => cartItem.id === item.id);

    if (existingCartItemIndex >= 0) {
      // 更新購物車中的商品數量
      const updatedCartItems = [...cartItems];

      if (newQuantity <= 0) {
        // 如果數量為 0 或負數，從購物車中移除
        updatedCartItems.splice(existingCartItemIndex, 1);
      } else {
        // 否則更新數量
        updatedCartItems[existingCartItemIndex].quantity = newQuantity;
      }

      setCartItems(updatedCartItems);
    } else if (newQuantity > 0) {
      // 如果購物車中沒有此商品且數量大於 0，新增至購物車
      setCartItems([...cartItems, { ...item, quantity: newQuantity }]);
    }
  };

  const removeCartItem = (itemId: string): void => {
    setCartItems(cartItems.filter(item => item.id !== itemId));
  };

  const calculateSubtotal = (): number => {
    return cartItems.reduce((total, item) => total + item.price * item.quantity, 0);
  };

  const calculateDiscount = async (): Promise<number> => {
    try {
      const projectDiscounts = await fetchProjectDiscounts(projectId);
      if (!projectDiscounts.length) {
        setAppliedDiscount(null);
        return 0;
      }

      // 找出最優惠的折扣規則
      let maxDiscount = 0;
      let bestDiscount = null;

      for (const discount of projectDiscounts) {
        const result = calculateQuantityTierDiscount({
          items: cartItems,
          discount,
        });

        if (result.discountAmount > maxDiscount) {
          maxDiscount = result.discountAmount;
          bestDiscount = discount;
        }
      }

      setAppliedDiscount(bestDiscount);
      return maxDiscount;
    } catch (error) {
      console.error('計算折扣時發生錯誤:', error);
      setAppliedDiscount(null);
      return 0;
    }
  };

  const calculateTotal = useCallback(async (): Promise<number> => {
    const discount = await calculateDiscount();
    return calculateSubtotal() - discount;
  }, [cartItems, projectId]);

  const getDiscountMessage = (): string => {
    if (!appliedDiscount) {
      return '';
    }

    return `已達成數量折扣：商品總數 ≥ ${appliedDiscount.quantity_threshold}，每件商品折抵 ${appliedDiscount.discount_per_item} 元`;
  };

  return {
    cartItems,
    handleQuantityChange,
    removeCartItem,
    calculateSubtotal,
    calculateDiscount,
    calculateTotal,
    getDiscountMessage,
    appliedDiscount,
  };
};

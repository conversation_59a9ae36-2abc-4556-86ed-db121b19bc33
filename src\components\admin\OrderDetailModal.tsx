import React, { useState } from 'react';
import type { Order } from '../../types/order';

interface Props {
  order: Order;
  onClose: () => void;
  onSave: (updated: Partial<Order>) => Promise<void>;
}

const OrderDetailModal: React.FC<Props> = ({ order, onClose, onSave }) => {
  const [manualDiscount, setManualDiscount] = useState<number>(order.manualDiscountAmount || 0);
  const [adminNotes, setAdminNotes] = useState<string>(order.adminNotes || '');
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    setSaving(true);
    try {
      await onSave({
        id: order.id,
        manualDiscountAmount: manualDiscount,
        adminNotes,
      });
      onClose();
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <h2>訂單詳情</h2>
        <p>訂單編號：{order.id}</p>
        <p>專案：{order.projectName}</p>
        <p>用戶：{order.user?.communityNickname || order.user?.name}</p>
        <p>訂單日期：{new Date(order.createdAt ?? order.date ?? '').toLocaleString()}</p>
        <p>總金額：NT$ {order.total.toLocaleString()}</p>
        <p>自動折扣（系統計算）：-NT$ {order.discountAmount?.toLocaleString() || '0'}</p>
        <p>手動折扣（管理員調整）：-NT$ {order.manualDiscountAmount?.toLocaleString() || '0'}</p>
        <p>最終金額：NT$ {order.finalAmount?.toLocaleString() || '0'}</p>
        <p>手動折扣操作人：{order.manualDiscountAppliedBy || '-'}</p>
        <p>
          手動折扣時間：
          {order.manualDiscountAppliedAt
            ? new Date(order.manualDiscountAppliedAt ?? '').toLocaleString()
            : '-'}
        </p>

        <div style={{ marginTop: '1em' }}>
          <label>手動折扣金額：</label>
          <input
            type="number"
            value={manualDiscount}
            onChange={e => setManualDiscount(Number(e.target.value))}
          />
        </div>

        <div style={{ marginTop: '1em' }}>
          <label>管理員備註：</label>
          <textarea
            value={adminNotes}
            onChange={e => setAdminNotes(e.target.value)}
            rows={3}
            style={{ width: '100%' }}
          />
        </div>

        <div style={{ marginTop: '1em' }}>
          <button onClick={handleSave} disabled={saving}>
            儲存
          </button>
          <button onClick={onClose} style={{ marginLeft: '1em' }}>
            取消
          </button>
        </div>
      </div>
    </div>
  );
};

export default OrderDetailModal;

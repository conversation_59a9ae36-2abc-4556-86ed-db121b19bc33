/**
 * 管理後台儀表板頁面樣式
 * 
 * 樣式結構：
 * 1. 容器樣式
 *    - 最大寬度和居中對齊
 *    - 響應式內邊距
 * 
 * 2. 頁面標題區塊
 *    - 標題和副標題樣式
 *    - 文字顏色和大小設定
 * 
 * 3. 功能卡片網格
 *    - 響應式網格布局
 *    - 卡片間距和對齊
 * 
 * 4. 卡片樣式
 *    - 背景色和邊框
 *    - 懸停效果和陰影
 *    - 內容排版和間距
 * 
 * 5. 響應式設計
 *    - 移動端適配
 *    - 字體大小調整
 *    - 網格布局變更
 * 
 * 使用變數：
 * - 顏色：使用 admin-color-* 變數
 * - 間距：使用 admin-spacing-* 變數
 * - 字體：使用 admin-font-* 變數
 * 
 * @version 2024-03-19
 */

/* 圖表容器 */
.chartContainer {
  background-color: var(--admin-bg-white);
  border-radius: var(--admin-radius-lg);
  box-shadow: var(--admin-shadow-sm);
  padding: var(--admin-space-lg);
  margin-bottom: var(--admin-space-lg);
}

.chartHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-md);
}

.chartTitle {
  font-size: var(--admin-font-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-dark);
}

.chartPlaceholder {
  height: 300px;
  background-color: var(--admin-bg-lighter);
  border-radius: var(--admin-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--admin-text-light);
  font-size: var(--admin-font-sm);
}

/* 最近活動 */
.recentActivity {
  background-color: var(--admin-bg-white);
  border-radius: var(--admin-radius-lg);
  box-shadow: var(--admin-shadow-sm);
  padding: var(--admin-space-lg);
}

.activityList {
  margin-top: var(--admin-space-md);
}

.activityItem {
  display: flex;
  padding: var(--admin-space-md) 0;
  border-bottom: 1px solid var(--admin-bg-light);
}

.activityItem:last-child {
  border-bottom: none;
}

.activityIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--admin-primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--admin-space-md);
  color: var(--admin-primary);
}

.activityContent {
  flex: 1;
}

.activityTitle {
  font-weight: var(--admin-font-semibold);
  margin-bottom: var(--admin-space-xxs);
  color: var(--admin-text-dark);
}

.activityTime {
  font-size: var(--admin-font-sm);
  color: var(--admin-text-light);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .chartContainer,
  .recentActivity {
    padding: var(--admin-space-md);
  }

  .chartPlaceholder {
    height: 200px;
  }

  .activityItem {
    padding: var(--admin-space-sm) 0;
  }

  .activityIcon {
    width: 32px;
    height: 32px;
    margin-right: var(--admin-space-sm);
  }
}

@media (max-width: 576px) {
  .chartContainer,
  .recentActivity {
    padding: var(--admin-space-sm);
  }

  .chartPlaceholder {
    height: 150px;
  }
}

.container {
  padding: var(--admin-spacing-6);
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: var(--admin-spacing-8);
  text-align: center;
}

.header h1 {
  font-size: var(--admin-font-size-3xl);
  font-weight: var(--admin-font-weight-bold);
  color: var(--admin-color-text);
  margin-bottom: var(--admin-spacing-2);
}

.header p {
  font-size: var(--admin-font-size-lg);
  color: var(--admin-color-text-light);
}

.cardGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--admin-spacing-6);
}

.card {
  background-color: var(--admin-color-white);
  border: 1px solid var(--admin-color-border);
  border-radius: var(--admin-border-radius-lg);
  padding: var(--admin-spacing-6);
  text-decoration: none;
  transition: all 0.2s ease-in-out;
  box-shadow: var(--admin-shadow-sm);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-md);
  border-color: var(--admin-color-primary);
}

.cardContent {
  display: flex;
  flex-direction: column;
  gap: var(--admin-spacing-2);
}

.cardTitle {
  font-size: var(--admin-font-size-xl);
  font-weight: var(--admin-font-weight-semibold);
  color: var(--admin-color-text);
}

.cardDescription {
  font-size: var(--admin-font-size-base);
  color: var(--admin-color-text-light);
  line-height: 1.5;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .container {
    padding: var(--admin-spacing-4);
  }

  .cardGrid {
    grid-template-columns: 1fr;
  }

  .header h1 {
    font-size: var(--admin-font-size-2xl);
  }

  .header p {
    font-size: var(--admin-font-size-base);
  }
} 
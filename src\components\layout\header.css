@import '../../styles/variables.css';

/* Header 容器 */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-fixed);
  height: var(--app-header-height);
  padding: 0 var(--space-md);
  background-color: var(--header-bg);
  color: var(--header-text);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-sm);
  
  /* Flexbox 布局 */
  display: flex;
  align-items: center; /* 確保所有子元素垂直置中 */
  justify-content: space-between;
  gap: var(--space-md);
}

/* Logo 區域 - 簡化樣式確保垂直置中 */
.app-header .logo {
  display: flex;
  align-items: center; /* 確保 logo 內部元素垂直置中 */
  text-decoration: none;
  height: 100%;
}

/* Logo 圖片 - 使用最簡單的方式確保垂直置中 */
.app-header .logo-img {
  width: 42px;
  height: 42px;
  margin-right: var(--space-sm);
  object-fit: contain;
  /* 重要：不使用 display: block，保持元素的自然行為 */
}

/* Logo 文字 - 確保與圖片對齊 */
.app-header .logo-text {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--header-text);
}

/* 右側導航 */
.app-header .nav-right {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  height: 100%;
}

/* 購物車按鈕 */
.app-header .cart-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 var(--space-sm);
  color: var(--header-text-light);
  background: none;
  border: none;
  cursor: pointer;
  transition: color var(--transition-fast);
}

.app-header .cart-btn:hover {
  color: var(--header-text);
}

.app-header .nav-icon.cart-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-header .cart-badge {
  position: absolute;
  top: 12px;
  right: 4px;
  min-width: 18px;
  height: 18px;
  padding: 0 4px;
  background-color: var(--secondary-color);
  color: var(--secondary-dark);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: bold;
  
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

/* 搜索表單 */
.app-header .search-form {
  flex: 1;
  max-width: 480px;
  position: relative;
  margin: 0 var(--space-md);
}

.app-header .search-input {
  width: 100%;
  height: 36px;
  padding: var(--space-sm) var(--space-xl);
  padding-right: var(--space-lg);
  background-color: var(--header-element-bg);
  border: 1px solid var(--header-element-border);
  border-radius: var(--radius-full);
  color: var(--header-text);
  font-size: var(--font-size-base);
  transition: all var(--transition-fast);
}

.app-header .search-input::placeholder {
  color: var(--header-text-light);
}

.app-header .search-input:focus {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  outline: none;
}

.app-header .search-btn {
  position: absolute;
  right: var(--space-sm);
  top: 50%;
  transform: translateY(-50%);
  padding: var(--space-xs);
  color: var(--header-text);
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-header .search-icon {
  width: 16px;
  height: 16px;
  opacity: 0.8;
}

/* 用戶控制 */
.app-header .user-control {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--space-xs);
}

.app-header .user-avatar {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background-color: var(--header-element-bg);
  border: 1px solid var(--header-element-border);
  color: var(--header-text);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-header .user-avatar:hover {
  transform: scale(1.05);
  background-color: rgba(255, 255, 255, 0.15);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .app-header {
    padding: 0 var(--space-sm);
  }
  
  .app-header .search-form {
    margin: 0 var(--space-sm);
    max-width: 200px;
  }
  
  .app-header .logo-text {
    display: none;
  }
}

@media (max-width: 480px) {
  .app-header .search-form {
    display: none;
  }
}

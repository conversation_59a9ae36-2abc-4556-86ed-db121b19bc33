/**
 * 小森活 - 專案管理 - 商品管理元件
 *
 * 主要功能：
 * - 顯示專案底下所有商品
 * - 新增、編輯、刪除商品
 *
 * 模組交互：
 * - 由 ProjectsPage 編輯專案 Dialog 中呼叫，傳入 projectId
 * - 直接操作 Supabase items 表
 * - 與 ProjectForm 無直接耦合
 *
 * 已完成：
 * - 商品列表顯示
 * - 新增商品
 * - 編輯商品
 * - 刪除商品
 *
 * 待辦：
 * - 商品排序
 * - 商品圖片上傳
 * - 商品多規格支援
 * - 權限驗證
 * - 更細緻的錯誤處理
 */

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Table,
  Button,
  Form,
  Input,
  InputNumber,
  Upload,
  Space,
  message,
  Select,
} from 'antd';
import { PlusOutlined, DeleteOutlined, DragOutlined } from '@ant-design/icons';
import { supabase } from '@/lib/supabase';
import type { Item } from '@/types/supabase';
import type { UploadFile } from 'antd/es/upload/interface';
import type { RcFile } from 'antd/es/upload';
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
  DroppableProvided,
  DraggableProvided,
} from 'react-beautiful-dnd';

const { TextArea } = Input;

interface ProjectItemsManagerProps {
  projectId: string;
  onClose: () => void;
}

interface ItemFormData {
  name: string;
  description?: string;
  price: number;
  image_url?: string;
  sort_order?: number;
}

const ProjectItemsManager: React.FC<ProjectItemsManagerProps> = ({ projectId, onClose }) => {
  const moveItem = async (fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= items.length) return;

    const newItems = Array.from(items);
    const [moved] = newItems.splice(fromIndex, 1);
    newItems.splice(toIndex, 0, moved);

    const updates = newItems.map((item, idx) => ({
      id: item.id,
      sort_order: idx,
      entity_type: 'item',
    }));

    try {
      for (const item of newItems) {
        const { error } = await supabase.rpc('update_item_sort_order', {
          p_item_id: item.id,
          p_sort_order: newItems.indexOf(item),
        });
        if (error) throw error;
      }
      setItems(newItems);
    } catch (err) {
      console.error('更新排序失敗:', err);
      message.error('更新排序失敗');
    }
  };
  const [items, setItems] = useState<Item[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<Item | null>(null);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [submitting, setSubmitting] = useState(false);

  const fetchItems = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('items')
        .select('*')
        .eq('project_id', projectId)
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('Supabase error:', error);
        message.error(`獲取商品列表失敗：${error.message}`);
        return;
      }
      setItems(data || []);
    } catch (err) {
      console.error('獲取商品列表失敗:', err);
      message.error(`獲取商品列表失敗：${(err as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchItems();
  }, [projectId]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);

      // 處理圖片上傳
      if (fileList[0]?.originFileObj) {
        const formData = new FormData();
        formData.append('file', fileList[0].originFileObj);
        formData.append('upload_preset', process.env.NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET || '');

        const response = await fetch(
          `https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload`,
          {
            method: 'POST',
            body: formData,
          }
        );

        const data = await response.json();
        if (data.error) {
          throw new Error(data.error.message);
        }

        values.image_url = data.secure_url;
      }

      if (editingItem) {
        // 更新商品
        const { error } = await supabase
          .from('items')
          .update({
            ...values,
            updated_at: new Date().toISOString(),
          })
          .eq('id', editingItem.id);

        if (error) throw error;
        message.success('商品更新成功');
      } else {
        // 新增商品
        const { error } = await supabase.from('items').insert([
          {
            ...values,
            project_id: projectId,
            sort_order: items.length,
            status: 'Available',
          },
        ]);

        if (error) throw error;
        message.success('商品新增成功');
      }

      setIsFormVisible(false);
      setEditingItem(null);
      form.resetFields();
      setFileList([]);
      fetchItems();
    } catch (err) {
      console.error('表單提交失敗:', err);
      message.error('表單提交失敗，請檢查輸入內容');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const { error } = await supabase.from('items').delete().eq('id', id);

      if (error) throw error;
      message.success('商品刪除成功');
      fetchItems();
    } catch (err) {
      console.error('刪除商品失敗:', err);
      message.error('刪除商品失敗');
    }
  };

  const beforeUpload = (file: RcFile) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上傳圖片檔案！');
      return false;
    }

    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('圖片必須小於 2MB！');
      return false;
    }

    return true;
  };

  const handleDragEnd = async (result: DropResult) => {
    if (!result.destination) return;

    const newItems = Array.from(items);
    const [reorderedItem] = newItems.splice(result.source.index, 1);
    newItems.splice(result.destination.index, 0, reorderedItem);

    // 更新排序
    const updates = newItems.map((item, index) => ({
      id: item.id,
      sort_order: index,
    }));

    try {
      const { error } = await supabase.from('items').upsert(updates);
      if (error) throw error;
      setItems(newItems);
    } catch (err) {
      console.error('更新排序失敗:', err);
      message.error('更新排序失敗');
    }
  };

  const handleEdit = (item: Item) => {
    setEditingItem(item);
    form.setFieldsValue(item);
    if (item.image_url) {
      setFileList([
        {
          uid: '-1',
          name: 'image.png',
          status: 'done',
          url: item.image_url,
        },
      ]);
    }
    setIsFormVisible(true);
  };

  const columns = [
    {
      title: '排序',
      key: 'sort',
      width: 50,
      render: () => <DragOutlined style={{ cursor: 'move' }} />,
    },
    {
      title: '商品圖片',
      dataIndex: 'image_url',
      key: 'image_url',
      render: (url: string) =>
        url ? (
          <img src={url} alt="商品圖片" style={{ width: 50, height: 50, objectFit: 'cover' }} />
        ) : null,
    },
    {
      title: '商品編號',
      dataIndex: 'item_id_display',
      key: 'item_id_display',
    },
    {
      title: '商品名稱',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '價格',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => `$${price.toLocaleString()}`,
    },
    {
      title: '狀態',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          Available: '可購買',
          Sold: '已售完',
          Reserved: '已預訂',
        };
        return statusMap[status as keyof typeof statusMap] || status;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Item) => (
        <Space>
          <Button
            onClick={() => {
              handleEdit(record);
            }}
          >
            編輯
          </Button>
          <Button danger onClick={() => handleDelete(record.id)}>
            刪除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Modal title="管理商品" open={true} onCancel={onClose} width={1000} footer={null}>
      <Button
        type="primary"
        onClick={() => {
          setEditingItem(null);
          form.resetFields();
          setFileList([]);
          setIsFormVisible(true);
        }}
        style={{ marginBottom: 16 }}
      >
        新增商品
      </Button>

      <DragDropContext onDragEnd={handleDragEnd}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <div style={{ display: 'flex', fontWeight: 'bold', gap: '8px' }}>
            <div style={{ width: '40px' }}>排序</div>
            <div style={{ flex: 1 }}>商品編號</div>
            <div style={{ flex: 1 }}>商品名稱</div>
            <div style={{ width: '80px' }}>價格</div>
            <div style={{ width: '80px' }}>狀態</div>
            <div style={{ width: '120px' }}>操作</div>
          </div>
          <Droppable droppableId="items">
            {(provided: DroppableProvided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}
              >
                {items.map((item, index) => (
                  <Draggable key={item.id} draggableId={item.id} index={index}>
                    {(provided: DraggableProvided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        style={{
                          display: 'grid',
                          gridTemplateColumns: '40px 1fr 1fr 80px 80px 120px',
                          alignItems: 'center',
                          gap: '8px',
                          padding: '8px 4px',
                          borderBottom: '1px solid #ddd',
                          background: '#fff',
                          ...provided.draggableProps.style,
                        }}
                      >
                        <div
                          {...provided.dragHandleProps}
                          style={{ width: '40px', cursor: 'grab' }}
                        >
                          <DragOutlined />
                        </div>
                        <div style={{ flex: 1 }}>{item.item_id_display}</div>
                        <div style={{ flex: 1 }}>{item.name}</div>
                        <div style={{ width: '80px' }}>{item.price}</div>
                        <div style={{ width: '80px' }}>{item.status}</div>
                        <div
                          style={{
                            width: '100px',
                            display: 'grid',
                            gridTemplateColumns: '1fr 1fr',
                            gridTemplateRows: '1fr 1fr',
                            gap: '4px',
                          }}
                        >
                          <Button size="small" onClick={() => handleEdit(item)}>
                            編輯
                          </Button>
                          <Button
                            size="small"
                            onClick={() => moveItem(index, index - 1)}
                            disabled={index === 0}
                          >
                            上移
                          </Button>
                          <Button
                            size="small"
                            danger
                            onClick={() =>
                              Modal.confirm({
                                title: '確定刪除此商品？',
                                content: '刪除後無法恢復，請確認。',
                                okText: '刪除',
                                okType: 'danger',
                                cancelText: '取消',
                                onOk: () => handleDelete(item.id),
                              })
                            }
                          >
                            刪除
                          </Button>
                          <Button
                            size="small"
                            onClick={() => moveItem(index, index + 1)}
                            disabled={index === items.length - 1}
                          >
                            下移
                          </Button>
                        </div>
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </div>
      </DragDropContext>

      <Modal
        title={editingItem ? '編輯商品' : '新增商品'}
        open={isFormVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsFormVisible(false);
          setEditingItem(null);
          form.resetFields();
          setFileList([]);
        }}
        confirmLoading={submitting}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="商品名稱"
            rules={[{ required: true, message: '請輸入商品名稱' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item name="description" label="商品描述">
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item name="price" label="價格" rules={[{ required: true, message: '請輸入價格' }]}>
            <InputNumber<any>
              min={0}
              formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => Number(value!.replace(/\$\s?|(,*)/g, ''))}
              style={{ width: '100%' }}
            />
          </Form.Item>

          {editingItem && (
            <Form.Item
              name="status"
              label="狀態"
              rules={[{ required: true, message: '請選擇狀態' }]}
            >
              <Select>
                <Select.Option value="Available">可購買</Select.Option>
                <Select.Option value="Sold">已售完</Select.Option>
                <Select.Option value="Reserved">已預訂</Select.Option>
              </Select>
            </Form.Item>
          )}

          <Form.Item label="商品圖片" help="建議尺寸: 800x800，檔案大小不超過 2MB">
            <Upload
              listType="picture-card"
              fileList={fileList}
              beforeUpload={beforeUpload}
              onChange={({ fileList }) => setFileList(fileList)}
              maxCount={1}
            >
              {fileList.length === 0 && (
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>上傳</div>
                </div>
              )}
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  );
};

export default ProjectItemsManager;

/* ProjectDetails styles */

.project-details {
  padding: var(--space-xl);
  border-radius: var(--radius-lg);
  background-color: var(--bg-white);
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.project-details:hover {
  box-shadow: var(--shadow-md);
}

.project-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.project-highlights {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-lg);
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.highlight-icon {
  font-size: 1.5rem;
  width: 32px;
  height: 32px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-light);
  border-radius: var(--radius-full);
}

.highlight-text {
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: 500;
}

.project-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-md);
  align-items: center;
}

.project-status {
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.status-active {
  background-color: var(--primary-light);
  color: var(--primary);
}

.status-ordering_ended {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.status-arrived {
  background-color: var(--success-light);
  color: var(--success-color);
}

.status-completed {
  background-color: var(--text-light);
  color: var(--text-medium);
}

.project-deadline {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.project-description {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: 1.6;
}

.project-gallery-container {
  width: 100%;
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--bg-light);
}

.project-gallery {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  gap: 0;
  height: 100%;
  padding: 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
  align-items: center;
}

.project-gallery::-webkit-scrollbar {
  display: none;
}

.project-gallery img,
.project-gallery .gallery-image {
  height: 100%;
  width: auto;
  object-fit: contain;
  scroll-snap-align: start;
  flex-shrink: 0;
}

.gallery-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 200px;
  background-color: var(--bg-light);
  color: var(--text-secondary);
  font-style: italic;
}

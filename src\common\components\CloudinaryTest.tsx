import { useState } from 'react';
import { ImageUploader } from './ImageUploader';

export const CloudinaryTest = () => {
  const [uploadedImages, setUploadedImages] = useState<
    Array<{
      url: string;
      publicId: string;
    }>
  >([]);

  const handleUploadSuccess = (result: { url: string; publicId: string }) => {
    setUploadedImages(prev => [...prev, result]);
  };

  const handleUploadError = (error: string) => {
    console.error('上傳失敗：', error);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>Cloudinary 上傳測試</h2>

      <div style={{ marginBottom: '20px' }}>
        <ImageUploader
          maxSizeMB={2}
          acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
          onUploadSuccess={handleUploadSuccess}
          onUploadError={handleUploadError}
        />
      </div>

      {uploadedImages.length > 0 && (
        <div>
          <h3>已上傳的圖片：</h3>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
              gap: '16px',
              marginTop: '16px',
            }}
          >
            {uploadedImages.map((image, index) => (
              <div
                key={index}
                style={{ border: '1px solid #ddd', padding: '8px', borderRadius: '4px' }}
              >
                <img
                  src={image.url}
                  alt={`上傳圖片 ${index + 1}`}
                  style={{ width: '100%', height: '150px', objectFit: 'cover' }}
                />
                <div style={{ fontSize: '12px', marginTop: '8px', wordBreak: 'break-all' }}>
                  <p>Public ID: {image.publicId}</p>
                  <a href={image.url} target="_blank" rel="noopener noreferrer">
                    查看原圖
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

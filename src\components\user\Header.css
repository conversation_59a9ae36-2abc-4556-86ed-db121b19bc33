/* Header styles for user frontend */

.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--app-header-height);
  padding: 0 var(--space-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--header-bg) !important;
  color: var(--header-text) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.app-header .logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.app-header .logo-img {
  width: 42px;
  height: 42px;
  margin-right: var(--space-sm);
  object-fit: contain;
}

.app-header .logo-text {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--header-text) !important;
}

.app-header .nav-right {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.app-header .cart-btn {
  position: relative;
  background: none;
  border: none;
  color: var(--header-text-light) !important;
  font-size: var(--font-size-lg);
  padding: var(--space-sm);
  cursor: pointer;
  line-height: 1;
}

.app-header .cart-btn:hover {
  color: var(--header-text) !important;
}

.app-header .cart-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  min-width: 18px;
  height: 18px;
  padding: 0 4px;
  background-color: var(--secondary-color);
  color: var(--secondary-dark);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: none;
}

/* 用戶頭像 */
.user-control {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  cursor: pointer;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-weight: bold;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.user-name {
  font-weight: 500;
  font-size: var(--font-size-base);
  color: var(--header-text) !important;
}

/* 頂部購物車圖示 */
.cart-btn .nav-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='9' cy='21' r='1'%3E%3C/circle%3E%3Ccircle cx='20' cy='21' r='1'%3E%3C/circle%3E%3Cpath d='M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6'%3E%3C/path%3E%3C/svg%3E");
  width: 24px;
  height: 24px;
}

.cart-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='9' cy='21' r='1'%3E%3C/circle%3E%3Ccircle cx='20' cy='21' r='1'%3E%3C/circle%3E%3Cpath d='M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6'%3E%3C/path%3E%3C/svg%3E");
  width: 24px;
  height: 24px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 只針對 header 內購物車圖示反白 */
.app-header .cart-icon {
  filter: invert(1);
}

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './LoginPage/styles.module.css';

const AdminLoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const navigate = useNavigate();

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    // 開發階段：任何帳密都可登入
    navigate('/admin');
  };

  return (
    <div className={styles.adminLogin}>
      <div className={styles.loginContainer}>
        <div className={styles.logo}>
          <img src="/forest-life-logo.png" alt="小森活" className={styles.logoImg} />
          <span className={styles.logoText}>小森活</span>
        </div>
        <h1>管理後台登入</h1>
        <p className={styles.subtitle}>使用管理員帳號登入管理專案、訂單與用戶</p>

        <form onSubmit={handleLogin}>
          <div className={styles.formGroup}>
            <label htmlFor="email">電子郵件</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={e => setEmail(e.target.value)}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="password">密碼</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={e => setPassword(e.target.value)}
              required
            />
          </div>

          <button type="submit" className={styles.loginBtn}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M9 12h6m-6-4h6m-6 8h6M7.2 4h9.6a2.4 2.4 0 0 1 2.4 2.4v15.2a2.4 2.4 0 0 1-2.4 2.4H7.2a2.4 2.4 0 0 1-2.4-2.4V6.4A2.4 2.4 0 0 1 7.2 4z" />
            </svg>
            登入管理後台
          </button>
        </form>

        <p className={styles.note}>※ 開發階段：任何帳密都可登入</p>
      </div>
    </div>
  );
};

export default AdminLoginPage;

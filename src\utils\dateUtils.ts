import { format } from 'date-fns';
import { zhTW } from 'date-fns/locale';

/**
 * 格式化日期時間
 * @param date 日期時間字串或 Date 物件
 * @returns 格式化後的日期時間字串
 */
export const formatDateTime = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, 'yyyy/MM/dd HH:mm:ss', { locale: zhTW });
};

/**
 * 格式化日期
 * @param date 日期字串或 Date 物件
 * @returns 格式化後的日期字串
 */
export const formatDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, 'yyyy/MM/dd', { locale: zhTW });
};

/**
 * 檢查日期是否有效
 * @param date 日期字串或 Date 物件
 * @returns 是否為有效日期
 */
export const isValidDate = (date: string | Date): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj instanceof Date && !isNaN(dateObj.getTime());
};

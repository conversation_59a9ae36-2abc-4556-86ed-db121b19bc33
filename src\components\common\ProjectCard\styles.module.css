@import '../../../styles/variables.css';

.card {
  background-color: var(--bg-white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  cursor: pointer;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.imageContainer {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 寬高比 */
  overflow: hidden;
}

.image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.card:hover .image {
  transform: scale(1.05);
}

.status {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  z-index: 1;
}

.statusPending {
  background-color: rgba(243, 156, 18, 0.1);
  color: var(--warning-color);
}

.statusActive {
  background-color: rgba(46, 204, 113, 0.1);
  color: var(--success-color);
}

.statusCompleted {
  background-color: rgba(52, 152, 219, 0.1);
  color: var(--info-color);
}

.content {
  padding: var(--space-lg);
}

.title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--space-xs);
  color: var(--text-dark);
}

.deadline {
  font-size: var(--font-size-sm);
  color: var(--text-medium);
  margin-bottom: var(--space-md);
}

.description {
  margin-bottom: var(--space-md);
  color: var(--text-medium);
  font-size: var(--font-size-md);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.highlights {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
  margin-bottom: var(--space-md);
}

.highlightItem {
  display: flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  background-color: var(--bg-light);
  border-radius: var(--radius-full);
}

.highlightIcon {
  margin-right: var(--space-xs);
  font-size: var(--font-size-md);
}

.highlightText {
  font-size: var(--font-size-xs);
  font-weight: 500;
  color: var(--text-medium);
}

.action {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--space-md);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .content {
    padding: var(--space-md);
  }
  
  .description {
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }
}

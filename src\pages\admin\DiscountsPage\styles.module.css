/* 折扣管理頁面樣式 */
.discountsContainer {
  width: 100%;
}

/* Modal 遮罩 */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* Modal 內容 - Using Hardcoded Values */
.modalContent {
  background: #ffffff; /* Hardcoded background */
  padding: 32px; /* Hardcoded padding */
  border-radius: 8px; /* Hardcoded radius */
  max-width: 600px;
  width: 95vw;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Hardcoded shadow */
  display: flex;
  flex-direction: column;
  gap: 24px; /* Hardcoded gap */
  border: 1px solid #e0e0e0; /* Hardcoded border color */
}

.modalTitle {
  margin: 0 0 24px 0; /* Hardcoded margin */
  color: #333333; /* Hardcoded color */
  font-size: 20px; /* Hardcoded font size */
  font-weight: 600; /* Hardcoded font weight */
  padding-bottom: 8px; /* Hardcoded padding */
  border-bottom: 1px solid #e0e0e0; /* Hardcoded border color */
}

.modalContent label {
  display: block;
  font-size: 14px; /* Hardcoded font size */
  font-weight: 500; /* Hardcoded font weight */
  color: #666666; /* Hardcoded color */
  margin-bottom: 4px; /* Hardcoded margin */
}

/* New Input Field Styling - Using Hardcoded Values */
.formInput {
  width: 100%;
  padding: 10px 16px; /* Hardcoded padding */
  border: 1px solid #ced4da; /* Softer border color */
  border-radius: 6px; /* Hardcoded radius */
  font-size: 16px; /* Hardcoded font size */
  color: #333333; /* Hardcoded color */
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  background-color: #ffffff; /* Hardcoded background */
}

.formInput:focus {
  border-color: #2c3e50; /* Hardcoded color */
  box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.15); /* Softer focus shadow */
  outline: none;
}

/* Special style for checkbox - Using Hardcoded Values */
.checkboxContainer {
  display: flex;
  align-items: center;
  gap: 8px; /* Hardcoded gap */
  cursor: pointer;
  padding: 4px 0;
}
.checkboxContainer input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin: 0;
  flex-shrink: 0;
}
.checkboxContainer label {
  margin-bottom: 0;
  font-weight: 400; /* Hardcoded font weight */
  color: #333333; /* Hardcoded color */
  line-height: 1;
}

/* Style for Select dropdown - Using Hardcoded Values */
.formSelect {
  composes: formInput;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 16px center; /* Hardcoded position */
  padding-right: calc(16px * 2 + 16px); /* Hardcoded padding */
}

/* Modal Buttons - Using Hardcoded Values */
.modalButtons {
  display: flex;
  justify-content: flex-end;
  gap: 16px; /* Hardcoded gap */
  margin-top: 24px; /* Hardcoded margin */
  padding-top: 24px; /* Hardcoded padding */
  border-top: 1px solid #e0e0e0; /* Hardcoded border color */
}

.modalButton {
  padding: 10px 24px; /* Increased vertical padding */
  border: none;
  border-radius: 6px; /* Hardcoded radius */
  font-size: 16px; /* Hardcoded font size */
  font-weight: 500; /* Hardcoded font weight */
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease, border-color 0.2s ease;
}
.modalButton:active {
  transform: translateY(1px);
}

.modalButtonPrimary {
  composes: modalButton;
  background-color: #2c3e50; /* Hardcoded color */
  color: #ffffff; /* Hardcoded color */
}
.modalButtonPrimary:hover {
  background-color: #1a252f; /* Hardcoded color */
}

.modalButtonSecondary {
  composes: modalButton;
  background-color: #f8f9fa; /* Hardcoded color */
  color: #666666; /* Hardcoded color */
  border: 1px solid #e0e0e0; /* Hardcoded color */
}
.modalButtonSecondary:hover {
  background-color: #e9ecef; /* Hardcoded color */
  border-color: #ced4da; /* Hardcoded color */
}

/* Keep other styles using variables if they work */
/* ... (rest of the file remains the same, using variables where applicable) ... */

/* 搜尋和篩選區域 */
.headerActions {
  display: flex;
  gap: var(--admin-space-md); /* Use variable */
}

.searchInput,
.filterSelect {
  padding: var(--admin-space-sm) var(--admin-space-md); /* Use variable */
  border: 1px solid var(--admin-border-color); /* Use variable */
  border-radius: var(--admin-radius-md); /* Use variable */
  font-size: var(--admin-font-base); /* Use variable */
  background-color: var(--admin-bg-white); /* Use variable */
  transition: all var(--admin-transition-normal); /* Use variable */
}

.searchInput:focus,
.filterSelect:focus {
  border-color: var(--admin-primary); /* Use variable */
  box-shadow: 0 0 0 3px var(--admin-primary-light); /* Use variable */
  outline: none;
}

.searchInput {
  width: 240px;
}

/* 折扣列表 */
.discountList {
  background-color: var(--admin-bg-white); /* Use variable */
  border-radius: var(--admin-radius-lg); /* Use variable */
  box-shadow: var(--admin-shadow-sm); /* Use variable */
  overflow: hidden;
  width: 100%;
}

/* 折扣碼顯示 */
.codeContainer {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xxs); /* Use variable */
}

.discountId {
  color: var(--admin-text-light); /* Use variable */
  font-size: var(--admin-font-sm); /* Use variable */
}

.discountCode {
  font-weight: var(--admin-font-semibold); /* Use variable */
  color: var(--admin-text-dark); /* Use variable */
}

/* 使用次數顯示 */
.usageContainer {
  display: flex;
  align-items: baseline;
  gap: var(--admin-space-xs); /* Use variable */
}

.usageCount {
  font-weight: var(--admin-font-semibold); /* Use variable */
  color: var(--admin-text-dark); /* Use variable */
}

.usageLimit {
  color: var(--admin-text-light); /* Use variable */
  font-size: var(--admin-font-sm); /* Use variable */
}

/* 有效期間顯示 */
.periodContainer {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xxs); /* Use variable */
  font-size: var(--admin-font-sm); /* Use variable */
  color: var(--admin-text-medium); /* Use variable */
}

/* 狀態標籤 */
.statusBadge {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-sm); /* Use variable */
  border-radius: var(--admin-radius-sm); /* Use variable */
  font-size: var(--admin-font-sm); /* Use variable */
  font-weight: var(--admin-font-medium); /* Use variable */
}

/* Assuming status colors are defined elsewhere or use defaults */
.statusactive {
  background-color: var(--admin-status-published-bg); /* Use variable */
  color: var(--admin-status-published-text); /* Use variable */
}

.statusexpired {
  background-color: var(--admin-status-archived-bg); /* Use variable */
  color: var(--admin-status-archived-text); /* Use variable */
}

.statusdisabled {
  background-color: var(--admin-status-draft-bg); /* Use variable */
  color: var(--admin-status-draft-text); /* Use variable */
}

/* 操作按鈕 */
.actionButtons {
  display: flex;
  gap: 20px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .headerActions {
    flex-direction: column;
    gap: var(--admin-space-sm);
  }

  .searchInput,
  .filterSelect {
    width: 100%;
  }

  .codeContainer {
    gap: var(--admin-space-xxs);
  }

  .usageContainer {
    flex-direction: column;
    gap: var(--admin-space-xxs);
  }

  .periodContainer {
    gap: var(--admin-space-xxs);
  }

  .actionButtons {
    flex-direction: column;
    gap: var(--admin-space-xs);
  }

  .statusBadge {
    padding: var(--admin-space-xxs) var(--admin-space-xs);
  }
}

/* 篩選區塊容器 */
.filterSection {
  background-color: #fff;
  padding: 1rem;
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

/* 篩選欄位 input/select */
.filterSection input,
.filterSection select {
  padding: 0.75rem 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  width: 200px;
}

.filterSection input:focus,
.filterSection select:focus {
  outline: none;
  border-color: #57AC5A;
  box-shadow: 0 0 0 3px rgba(87, 172, 90, 0.1);
}

/* 表格卡片容器 */
.tableCard {
  background-color: #fff;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

/* 表格標題行 */
.tableCard th {
  font-weight: 600;
  font-size: 15px;
  color: #333;
  padding: 12px 16px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ddd;
}

/* 表格資料行 */
.tableCard td {
  font-size: 14px;
  color: #333;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
}

/* 表格行 hover */
.tableCard tr:hover {
  background-color: rgba(87, 172, 90, 0.05);
}

/* 編輯按鈕 */
.editButton {
  border: 2px solid #8BC48A;
  color: #8BC48A;
  background-color: transparent;
  border-radius: 8px;
  padding: 6px 14px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.editButton:hover {
  background-color: rgba(139, 196, 138, 0.1);
}

/* 刪除按鈕 */
.deleteButton {
  border: 2px solid #F18C8E;
  color: #F18C8E;
  background-color: transparent;
  border-radius: 8px;
  padding: 6px 14px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.deleteButton:hover {
  background-color: rgba(241, 140, 142, 0.1);
}

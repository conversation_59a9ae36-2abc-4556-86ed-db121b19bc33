/**
 * 商品介面
 */
export interface Item {
  /**
   * 商品唯一識別碼
   */
  readonly id: string;
  /**
   * 商品名稱
   */
  readonly name: string;
  /**
   * 商品描述
   */
  readonly description: string;
  /**
   * 商品價格
   */
  readonly price: number;
  /**
   * 商品庫存數量
   */
  readonly stock: number;
  /**
   * 商品圖片 URL
   */
  readonly imageUrl?: string;
  /**
   * 商品已選數量
   */
  readonly quantity?: number;
}

/**
 * 專案介面
 */
export interface Project {
  /**
   * 專案唯一識別碼
   */
  readonly id: string;
  /**
   * 專案顯示用 ID
   */
  readonly project_id_display: string;
  /**
   * 專案名稱
   */
  readonly name: string;
  /**
   * 專案描述
   */
  readonly description: string | null;
  /**
   * 專案圖片 URL 陣列
   */
  readonly images: string[];
  /**
   * 專案狀態
   */
  readonly project_status: string;
  /**
   * 預設折扣 ID
   */
  readonly default_discount_id: string | null;
  /**
   * 建立時間
   */
  readonly created_at: string;
  /**
   * 更新時間
   */
  readonly updated_at: string;
  /**
   * 擁有者 ID
   */
  readonly owner_id: string;
  /**
   * 專案特點標籤
   */
  readonly highlights: Array<{
    /**
     * 特點圖標
     */
    readonly icon: string;
    /**
     * 特點文字
     */
    readonly text: string;
  }>;
  /**
   * 專案截止日期
   */
  readonly deadline: string | null;
  /**
   * 到貨日期
   */
  readonly arrival_date: string | null;
  /**
   * 擁有者資訊
   */
  readonly owner?: {
    display_name: string;
    community_nickname: string | null;
  };
}

/**
 * 購物車商品介面
 */
export interface CartItem extends Item {
  /**
   * 商品數量
   */
  quantity: number;
}

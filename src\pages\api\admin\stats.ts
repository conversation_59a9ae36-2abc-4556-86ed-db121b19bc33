import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const response = await fetch('http://localhost:3000/mcp/supabase/query', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        sql: "SELECT COUNT(*) AS total_orders, COALESCE(SUM(final_amount),0) AS total_sales FROM orders WHERE status IN ('Confirmed', 'Completed');",
      }),
    });
    const data = await response.json();
    const result = data[0] ?? { total_orders: 0, total_sales: 0 };
    res.status(200).json({
      totalOrders: parseInt(result.total_orders, 10) || 0,
      totalSales: parseFloat(result.total_sales) || 0,
    });
  } catch (error) {
    console.error('Fetch stats error', error);
    res.status(500).json({ totalOrders: 0, totalSales: 0 });
  }
}

import React, { useState } from 'react';
import {
  Form,
  Input,
  InputNumber,
  DatePicker,
  Switch,
  Select,
  Button,
  message,
  ConfigProvider,
} from 'antd';
import { Project, Item } from '../../types/models';
import { Discount } from '../../types/discount';
import { supabase } from '../../lib/supabaseClient';
import styles from './DiscountForm.module.css';

interface DiscountFormProps {
  projects: Project[];
  onProjectChange: (projectId: string) => Promise<Item[]>;
  initialValues?: Partial<Discount>;
  onSubmit: (values: any) => Promise<void>;
}

export const DiscountForm: React.FC<DiscountFormProps> = ({
  projects,
  onProjectChange,
  initialValues,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const [items, setItems] = useState<Item[]>([]);
  const [loading, setLoading] = useState(false);

  const handleProjectChange = async (projectId: string) => {
    try {
      const projectItems = await onProjectChange(projectId);
      setItems(projectItems);
    } catch (error) {
      message.error('載入商品失敗');
      console.error('Error loading items:', error);
    }
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      await onSubmit(values);
      message.success('儲存成功');
      form.resetFields();
    } catch (error) {
      message.error('儲存失敗');
      console.error('Error saving discount:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#1976D2',
          borderRadius: 8,
          fontFamily: '"Noto Sans TC", "Roboto", "Helvetica", "Arial", sans-serif',
        },
      }}
    >
      <div className={styles.formRoot}>
        <Form form={form} layout="vertical" initialValues={initialValues} onFinish={handleSubmit}>
          <Form.Item
            name="name"
            label="折扣名稱"
            rules={[{ required: true, message: '請輸入折扣名稱' }]}
          >
            <Input placeholder="例如：買二送一" />
          </Form.Item>

          <Form.Item name="description" label="折扣說明">
            <Input.TextArea placeholder="詳細說明折扣規則" />
          </Form.Item>

          <Form.Item
            name="projectId"
            label="適用專案"
            rules={[{ required: true, message: '請選擇適用專案' }]}
          >
            <Select placeholder="選擇專案" onChange={handleProjectChange}>
              {projects.map(project => (
                <Select.Option key={project.id} value={project.id}>
                  {project.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="applicableItemIds" label="適用商品" help="不選擇表示適用於所有商品">
            <Select mode="multiple" placeholder="選擇適用商品" allowClear>
              {items.map(item => (
                <Select.Option key={item.id} value={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="quantityThreshold"
            label="數量門檻"
            rules={[
              { required: true, message: '請輸入數量門檻' },
              { type: 'number', min: 1, message: '門檻必須大於 0' },
            ]}
          >
            <InputNumber min={1} placeholder="達到此數量才套用折扣" />
          </Form.Item>

          <Form.Item
            name="discountPerItem"
            label="每件折扣金額"
            rules={[
              { required: true, message: '請輸入折扣金額' },
              { type: 'number', min: 0, message: '折扣金額不可為負數' },
            ]}
          >
            <InputNumber min={0} placeholder="每件商品折抵金額" />
          </Form.Item>

          <Form.Item name="dateRange" label="有效期間">
            <DatePicker.RangePicker showTime placeholder={['開始時間', '結束時間']} />
          </Form.Item>

          <Form.Item name="active" label="啟用狀態" valuePropName="checked" initialValue={true}>
            <Switch />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              儲存
            </Button>
          </Form.Item>
        </Form>
      </div>
    </ConfigProvider>
  );
};

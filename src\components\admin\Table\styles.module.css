/* 引入後台變數 */
@import '../../../styles/admin-variables.css';

/* 表格容器 */
.tableWrapper {
  display: flex;
  flex-direction: column;
  gap: var(--admin-spacing-4);
  width: 100%;
}

.tableContainer {
  width: 100%;
  overflow-x: auto;
  background-color: var(--admin-color-white);
  border: 1px solid var(--admin-color-border);
  border-radius: var(--admin-border-radius-lg);
  box-shadow: var(--admin-shadow-sm);
}

/* 表格本體 */
.table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
}

/* 表頭 */
.tableHeader {
  padding: var(--admin-spacing-4);
  background-color: var(--admin-color-gray-50);
  font-weight: var(--admin-font-weight-medium);
  color: var(--admin-color-text);
  border-bottom: 1px solid var(--admin-color-border);
  white-space: nowrap;
}

/* 表格行 */
.tableRow {
  transition: background-color 0.2s ease-in-out;
}

.tableRow:hover {
  background-color: var(--admin-color-gray-50);
}

/* 表格單元格 */
.tableCell {
  padding: var(--admin-spacing-4);
  border-bottom: 1px solid var(--admin-color-border);
  color: var(--admin-color-text);
}

.tableRow:last-child .tableCell {
  border-bottom: none;
}

/* 狀態標籤 */
.statusBadge {
  display: inline-flex;
  align-items: center;
  padding: var(--admin-space-xs) var(--admin-space-md);
  border-radius: 9999px;
  font-size: var(--admin-font-xs);
  font-weight: var(--admin-font-semibold);
  text-transform: uppercase;
  line-height: 1.2;
}

.statusSuccess {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--admin-success);
}

.statusWarning {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--admin-warning);
}

.statusError {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--admin-error);
}

.statusInfo {
  background-color: rgba(33, 150, 243, 0.1);
  color: var(--admin-info);
}

/* 操作按鈕容器 */
.actionContainer {
  display: flex;
  gap: var(--admin-space-sm);
  align-items: center;
}

/* 分頁樣式 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-spacing-2);
  padding: var(--admin-spacing-4);
}

.pageButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  padding: var(--admin-spacing-2) var(--admin-spacing-3);
  border: 1px solid var(--admin-color-border);
  border-radius: var(--admin-border-radius-md);
  background-color: var(--admin-color-white);
  color: var(--admin-color-text);
  font-size: var(--admin-font-size-sm);
  font-weight: var(--admin-font-weight-medium);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.pageButton:hover:not(.pageButtonDisabled) {
  background-color: var(--admin-color-gray-50);
  border-color: var(--admin-color-border-dark);
}

.pageButtonActive {
  background-color: var(--admin-color-primary);
  border-color: var(--admin-color-primary);
  color: var(--admin-color-white);
}

.pageButtonActive:hover {
  background-color: var(--admin-color-primary-dark);
  border-color: var(--admin-color-primary-dark);
}

.pageButtonDisabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 響應式設計 */
@media (max-width: 640px) {
  .tableContainer {
    border-radius: 0;
  }

  .table {
    display: block;
  }

  .tableHeader {
    display: none;
  }

  .tableRow {
    display: block;
    padding: var(--admin-spacing-4);
    border-bottom: 1px solid var(--admin-color-border);
  }

  .tableCell {
    display: flex;
    padding: var(--admin-spacing-2) 0;
    border: none;
  }

  .tableCell::before {
    content: attr(data-label);
    flex: 0 0 30%;
    font-weight: var(--admin-font-weight-medium);
    color: var(--admin-color-text-light);
  }

  .pagination {
    flex-wrap: wrap;
  }
} 
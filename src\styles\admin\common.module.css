/* 管理介面共用樣式 */

.pageContainer {
  padding: var(--spacing-lg);
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.pageTitle {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.headerActions {
  display: flex;
  gap: var(--spacing-sm);
}

.searchInput {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  width: 240px;
  font-size: var(--font-size-sm);
}

.filterSelect {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  background-color: var(--bg-primary);
}

/* 表格容器 */
.tableContainer {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-md);
  min-height: 400px;
  overflow-x: auto;
}

/* 表格樣式 */
.table {
  width: 100%;
  border-collapse: collapse;
}

.tableHeader th {
  padding: var(--spacing-md);
  text-align: left;
  font-weight: 600;
  color: var(--text-secondary);
  background-color: var(--bg-light);
  border-bottom: 1px solid var(--border-color);
}

.tableRow td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.tableRow:hover {
  background-color: var(--bg-light);
}

/* 狀態標籤 */
.badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  display: inline-block;
}

.badgeAdmin {
  composes: badge;
  background-color: var(--secondary-light);
  color: var(--secondary-dark);
}

.badgeUser {
  composes: badge;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
}

.badgeActive {
  composes: badge;
  background-color: var(--primary-light);
  color: var(--primary-dark);
}

.badgeInactive {
  composes: badge;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
}

.badgeExpired {
  composes: badge;
  background-color: #fbe9e7;
  color: #d32f2f;
}

/* 操作按鈕 */
.actionButton {
  padding: var(--spacing-xs) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-sm);
  margin-right: var(--spacing-sm);
  cursor: pointer;
  font-size: var(--font-size-xs);
  font-weight: 500;
  transition: opacity 0.2s ease;
}

.actionButtonView {
  composes: actionButton;
  background-color: var(--secondary-light);
  color: var(--secondary-dark);
}

.actionButtonEdit {
  composes: actionButton;
  background-color: #fff3dc;
  color: #b25e02;
}

.actionButton:hover {
  opacity: 0.8;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .pageContainer {
    padding: var(--spacing-md);
  }

  .pageHeader {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .headerActions {
    width: 100%;
    flex-wrap: wrap;
  }

  .searchInput {
    width: 100%;
  }

  .tableContainer {
    padding: var(--spacing-sm);
  }

  .table {
    font-size: var(--font-size-sm);
  }

  .tableHeader th,
  .tableRow td {
    padding: var(--spacing-sm);
  }
} 
@import '../../../styles/variables.css';

.card {
  display: flex;
  align-items: center;
  padding: var(--space-md);
  border-radius: var(--radius-md);
  background-color: var(--bg-white);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--space-md);
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.imageContainer {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-right: var(--space-md);
  flex-shrink: 0;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.info {
  flex: 1;
  min-width: 0;
  margin-right: var(--space-md);
}

.name {
  font-size: var(--font-size-md);
  font-weight: 600;
  margin-bottom: var(--space-xs);
  color: var(--text-dark);
}

.description {
  font-size: var(--font-size-sm);
  color: var(--text-medium);
  margin-bottom: var(--space-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stock {
  font-size: var(--font-size-xs);
  font-weight: 600;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  display: inline-block;
}

.lowStock {
  background-color: rgba(243, 156, 18, 0.1);
  color: var(--warning-color);
}

.outOfStock {
  background-color: rgba(231, 76, 60, 0.1);
  color: var(--error-color);
}

.price {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-dark);
  margin-right: var(--space-md);
  white-space: nowrap;
}

.quantityControl {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.quantityBtn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-light);
  border: none;
  cursor: pointer;
  font-size: var(--font-size-md);
  font-weight: 600;
  transition: background-color var(--transition-fast);
}

.quantityBtn:hover:not(:disabled) {
  background-color: rgba(0, 0, 0, 0.05);
}

.quantityBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantityInput {
  width: 40px;
  height: 32px;
  border: none;
  text-align: center;
  font-size: var(--font-size-sm);
  font-weight: 500;
  -moz-appearance: textfield;
  appearance: textfield; /* 標準屬性，確保兼容性 */
}

.quantityInput::-webkit-outer-spin-button,
.quantityInput::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.quantityInput:focus {
  outline: none;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .card {
    flex-wrap: wrap;
  }
  
  .info {
    width: calc(100% - 80px - var(--space-md));
    margin-right: 0;
    margin-bottom: var(--space-sm);
  }
  
  .price {
    order: 3;
    margin-left: calc(80px + var(--space-md));
    margin-right: auto;
  }
  
  .quantityControl {
    order: 4;
  }
}

@media (max-width: 480px) {
  .imageContainer {
    width: 60px;
    height: 60px;
  }
  
  .info {
    width: calc(100% - 60px - var(--space-md));
  }
  
  .price {
    margin-left: calc(60px + var(--space-md));
  }
}

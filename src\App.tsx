/**
 * 應用程式主元件 - 設置路由和主題系統
 *
 * 路由結構：
 * 1. 前台路由
 *    - /           首頁
 *    - /preorder   預購頁面
 *    - /order-history 訂單歷史
 *
 * 2. 後台路由 (/admin/*)
 *    - /admin          管理儀表板（首頁）
 *    - /admin/login    後台登入
 *    - /admin/projects 專案管理
 *    - /admin/orders   訂單管理
 *    - /admin/users    用戶管理
 *    - /admin/discounts 折扣管理
 *
 * 特點：
 * - 使用巢狀路由結構
 * - 前台和後台使用不同的主題和佈局
 * - 支援深色模式切換
 * - 未匹配路由重定向到首頁
 *
 * @version 2024-03-19
 */

import React from 'react';
import { BrowserRouter, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { theme, adminTheme } from './styles/theme';

// === 佈局元件 ===
import UserLayout from './components/layout/UserLayout';
import AdminLayout from './components/layout/AdminLayout';

// === 前台頁面 ===
import HomePage from './pages/home/<USER>';
import PreorderPage from './pages/preorder/PreorderPage';
import OrderHistoryPage from './pages/order-history/OrderHistoryPage';
import TestHeaderPage from './pages/test-header';

// === 後台頁面 ===
// === 後台頁面 ===
import AdminLoginPage from './pages/admin/LoginPage';
import DashboardPage from './pages/admin/DashboardPage';
import ProjectsPage from './pages/admin/ProjectsPage';
import OrdersPage from './pages/admin/orders';
import UsersPage from './pages/admin/UsersPage';
import DiscountsPage from './pages/admin/DiscountsPage';
import DiscountProjectMappingPage from './pages/admin/DiscountProjectMappingPage';

// === 樣式 ===
import './styles/reset.css';
import './styles/variables.css';

/**
 * 路由包裝器 - 根據路徑切換主題
 */
const ThemedRoutes: React.FC = () => {
  const location = useLocation();
  const isAdmin = location.pathname.startsWith('/admin');
  const currentTheme = isAdmin ? adminTheme : theme;

  // 設置 data-theme 屬性以應用對應的 CSS 變數
  React.useEffect(() => {
    document.documentElement.setAttribute('data-theme', isAdmin ? 'admin' : 'default');
  }, [isAdmin]);

  return (
    <ThemeProvider theme={currentTheme}>
      <Routes>
        {/* === 前台路由 === */}
        <Route element={<UserLayout />}>
          <Route path="/" element={<HomePage />} />
          <Route path="/preorder" element={<PreorderPage />} />
          <Route path="/order-history" element={<OrderHistoryPage />} />
          <Route path="/test-header" element={<TestHeaderPage />} />
        </Route>

        {/* === 後台路由 === */}
        <Route path="/admin/login" element={<AdminLoginPage />} />
        <Route path="/admin" element={<AdminLayout />}>
          <Route index element={<DashboardPage />} />
          <Route path="projects" element={<ProjectsPage />} />
          <Route path="orders" element={<OrdersPage />} />
          <Route path="users" element={<UsersPage />} />
          <Route path="discounts" element={<DiscountsPage />} />
          <Route path="discount-project-mapping" element={<DiscountProjectMappingPage />} />
        </Route>

        {/* === 未匹配路由重定向到首頁 === */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </ThemeProvider>
  );
};

/**
 * 應用程式主元件
 */
import { CartProvider } from './contexts/CartContext';

const App: React.FC = () => {
  return (
    <BrowserRouter>
      <CartProvider>
        <CssBaseline />
        <ThemedRoutes />
      </CartProvider>
    </BrowserRouter>
  );
};

export default App;

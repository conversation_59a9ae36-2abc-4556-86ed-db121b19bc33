/* 
  注意：
  此檔案為首頁 BottomNav 元件，已經確認過，禁止任何修改！
  如需調整請先徵求確認，避免破壞首頁設計。
*/

import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import './BottomNav.css';

const BottomNav: React.FC = () => {
  const location = useLocation();

  return (
    <nav className="bottom-nav">
      <Link to="/" className={`nav-item ${location.pathname === '/' ? 'active' : ''}`}>
        <span className="nav-icon home-icon"></span>
        <span className="nav-label">首頁</span>
      </Link>
      <Link
        to="/preorder"
        className={`nav-item ${location.pathname.startsWith('/preorder') ? 'active' : ''}`}
      >
        <span className="nav-icon cart-icon"></span>
        <span className="nav-label">預購</span>
      </Link>
      <Link
        to="/order-history"
        className={`nav-item ${location.pathname.startsWith('/order-history') ? 'active' : ''}`}
      >
        <span className="nav-icon order-icon"></span>
        <span className="nav-label">我的訂單</span>
      </Link>
    </nav>
  );
};

export default BottomNav;

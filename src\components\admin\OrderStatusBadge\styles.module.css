.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-font-sm);
  font-weight: var(--admin-font-medium);
  line-height: 1;
  white-space: nowrap;
}

:global(.badge-pending) {
  background-color: var(--admin-warning-light);
  color: var(--admin-warning-dark);
}

:global(.badge-processing) {
  background-color: var(--admin-info-light);
  color: var(--admin-info-dark);
}

:global(.badge-completed) {
  background-color: var(--admin-success-light);
  color: var(--admin-success-dark);
}

:global(.badge-cancelled) {
  background-color: var(--admin-error-light);
  color: var(--admin-error-dark);
}

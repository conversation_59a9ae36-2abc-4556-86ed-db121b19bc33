.container {
  padding: 2rem;
  background-color: #f5f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  overflow: visible;
}

/* 主卡片 */
.contentCard {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* 頁首 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.actions {
  display: flex;
  gap: 0.5rem;
}

/* 重置按鈕 */
.resetButton {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 2px solid #57AC5A;
  background-color: transparent;
  color: #57AC5A;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.resetButton:hover:not(:disabled) {
  background-color: rgba(87, 172, 90, 0.05);
}

.resetButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 篩選區 */
.filters {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  position: relative;
}

/* 篩選行 */
.filterRow {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

/* 篩選欄 */
.filterGroup {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
}

.filterLabel {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #666;
}

.filterInput,
.filterSelect {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.filterInput:focus,
.filterSelect:focus {
  outline: none;
  border-color: #57AC5A;
  box-shadow: 0 0 0 3px rgba(87, 172, 90, 0.1);
}

/* 批次操作 */
.batchActions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin: 0.5rem 0;
  padding: 0;
  background: none;
  box-shadow: none;
}

.batchButton {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  border: 2px solid #57AC5A;
  background-color: transparent;
  color: #57AC5A;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.batchButton:hover:not(:disabled) {
  background-color: rgba(87, 172, 90, 0.05);
}

.batchButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 危險按鈕 */
.batchButton.danger {
  border-color: #e74c3c;
  color: #e74c3c;
}

.batchButton.danger:hover:not(:disabled) {
  background-color: rgba(231, 76, 60, 0.05);
}

/* 已選取數 */
.selectedCount {
  align-self: center;
  font-size: 0.9rem;
  color: #666;
}

/* 表格外層卡片 */
.tableWrapper {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  padding: 1rem;
  overflow-x: auto;
  flex: 1;
  overflow-y: auto;
}

/* 分頁 */
.footer {
  display: flex;
  justify-content: center;
  padding-top: 2rem;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pageButton {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: 1px solid #ddd;
  background-color: #fff;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pageButton:hover:not(:disabled) {
  border-color: #57AC5A;
  color: #57AC5A;
}

.pageButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageInfo {
  font-size: 0.9rem;
  color: #666;
}

/* 響應式 */
@media (max-width: 992px) {
  .filterRow {
    flex-direction: column;
  }
  .batchActions {
    justify-content: center;
  }
}

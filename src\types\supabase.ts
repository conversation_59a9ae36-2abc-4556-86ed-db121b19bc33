export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export interface Database {
  public: {
    Tables: {
      projects: {
        Row: {
          id: string;
          project_id_display: string;
          name: string;
          description: string | null;
          project_status: 'active' | 'ordering_ended' | 'arrived' | 'completed';
          images: string[] | null;
          default_discount_id: string | null;
          highlights:
            | {
                icon: string;
                text: string;
              }[]
            | null;
          deadline: string | null;
          arrival_date: string | null;
          created_at: string;
          updated_at: string;
          owner_id: string;
        };
        Insert: {
          id?: string;
          project_id_display: string;
          name: string;
          description?: string | null;
          project_status?: 'active' | 'ordering_ended' | 'arrived' | 'completed';
          images?: string[] | null;
          default_discount_id?: string | null;
          highlights?:
            | {
                icon: string;
                text: string;
              }[]
            | null;
          deadline?: string | null;
          arrival_date?: string | null;
          created_at?: string;
          updated_at?: string;
          owner_id: string;
        };
        Update: {
          id?: string;
          project_id_display?: string;
          name?: string;
          description?: string | null;
          project_status?: 'active' | 'ordering_ended' | 'arrived' | 'completed';
          images?: string[] | null;
          default_discount_id?: string | null;
          highlights?:
            | {
                icon: string;
                text: string;
              }[]
            | null;
          deadline?: string | null;
          arrival_date?: string | null;
          created_at?: string;
          updated_at?: string;
          owner_id?: string;
        };
      };
      items: {
        Row: {
          id: string;
          item_id_display: string;
          project_id: string;
          name: string;
          price: number;
          description: string | null;
          status: 'Available' | 'SoldOut' | 'Hidden';
          created_at: string;
          updated_at: string;
          image_url: string | null;
          sort_order: number | null;
        };
        Insert: {
          id?: string;
          item_id_display: string;
          project_id: string;
          name: string;
          price: number;
          description?: string | null;
          status?: 'Available' | 'SoldOut' | 'Hidden';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          item_id_display?: string;
          project_id?: string;
          name?: string;
          price?: number;
          description?: string | null;
          status?: 'Available' | 'SoldOut' | 'Hidden';
          created_at?: string;
          updated_at?: string;
        };
      };
      discounts: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          type: 'quantity_tier';
          quantity_threshold: number;
          discount_per_item: number;
          applicable_item_ids: string[] | null;
          active: boolean;
          start_date: string | null;
          end_date: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          type: 'quantity_tier';
          quantity_threshold: number;
          discount_per_item: number;
          applicable_item_ids?: string[] | null;
          active?: boolean;
          start_date?: string | null;
          end_date?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          type?: 'quantity_tier';
          quantity_threshold?: number;
          discount_per_item?: number;
          applicable_item_ids?: string[] | null;
          active?: boolean;
          start_date?: string | null;
          end_date?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      orders: {
        Row: {
          id: string;
          order_number: string;
          user_id: string;
          user_name: string;
          project_id: string;
          project_name: string;
          total_amount: number;
          discount_amount: number;
          applied_discount_id: string | null;
          manual_discount_amount: number;
          admin_notes: string | null;
          manual_discount_applied_by: string | null;
          manual_discount_applied_at: string | null;
          final_amount: number;
          status: 'Pending' | 'Confirmed' | 'Cancelled' | 'Completed';
          order_date: string;
          pickup_date: string | null;
          notes: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          order_number: string;
          user_id: string;
          user_name: string;
          project_id: string;
          project_name: string;
          total_amount: number;
          discount_amount?: number;
          applied_discount_id?: string | null;
          manual_discount_amount?: number;
          admin_notes?: string | null;
          manual_discount_applied_by?: string | null;
          manual_discount_applied_at?: string | null;
          final_amount: number;
          status?: 'Pending' | 'Confirmed' | 'Cancelled' | 'Completed';
          order_date?: string;
          pickup_date?: string | null;
          notes?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          order_number?: string;
          user_id?: string;
          user_name?: string;
          project_id?: string;
          project_name?: string;
          total_amount?: number;
          discount_amount?: number;
          applied_discount_id?: string | null;
          manual_discount_amount?: number;
          admin_notes?: string | null;
          manual_discount_applied_by?: string | null;
          manual_discount_applied_at?: string | null;
          final_amount?: number;
          status?: 'Pending' | 'Confirmed' | 'Cancelled' | 'Completed';
          order_date?: string;
          pickup_date?: string | null;
          notes?: string | null;
          created_at?: string;
        };
      };
      order_items: {
        Row: {
          id: string;
          order_id: string;
          item_id: string;
          item_name: string;
          quantity: number;
          unit_price: number;
          subtotal: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          order_id: string;
          item_id: string;
          item_name: string;
          quantity: number;
          unit_price: number;
          subtotal: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          order_id?: string;
          item_id?: string;
          item_name?: string;
          quantity?: number;
          unit_price?: number;
          subtotal?: number;
          created_at?: string;
        };
      };
      users: {
        Row: {
          id: string;
          display_name: string;
          community_nickname: string | null;
          picture_url: string | null;
          role: 'admin' | 'manager' | 'user';
          created_at: string;
          last_login_at: string | null;
        };
        Insert: {
          id: string;
          display_name: string;
          community_nickname?: string | null;
          picture_url?: string | null;
          role?: 'admin' | 'manager' | 'user';
          created_at?: string;
          last_login_at?: string | null;
        };
        Update: {
          id?: string;
          display_name?: string;
          community_nickname?: string | null;
          picture_url?: string | null;
          role?: 'admin' | 'manager' | 'user';
          created_at?: string;
          last_login_at?: string | null;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      generate_display_id: {
        Args: { prefix: string };
        Returns: string;
      };
      create_order_with_items: {
        Args: {
          p_user_id: string;
          p_project_id: string;
          p_items: Json[];
        };
        Returns: Json;
      };
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

export type Project = Database['public']['Tables']['projects']['Row'];
export type Item = Database['public']['Tables']['items']['Row'];
export type Discount = Database['public']['Tables']['discounts']['Row'];

.order-table {
  width: 100%;
  overflow-x: auto;
}

.order-table table {
  width: 100%;
  border-collapse: collapse;
}

.order-table th,
.order-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.order-table th {
  font-weight: 600;
  color: #666;
  background-color: #f9f9f9;
}

.order-table tr:hover {
  background-color: #f5f5f5;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.pending {
  background-color: #fff3dc;
  color: #b25e02;
}

.status-badge.processing {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-badge.completed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-badge.cancelled {
  background-color: #fbe9e7;
  color: #d32f2f;
}

.action-btn {
  padding: 4px 12px;
  border: none;
  border-radius: 4px;
  margin-right: 8px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
}

.action-btn.view {
  background-color: #e3f2fd;
  color: #1976d2;
}

.action-btn.edit {
  background-color: #fff3dc;
  color: #b25e02;
}

.action-btn:hover {
  opacity: 0.8;
} 
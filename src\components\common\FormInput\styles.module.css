@import '../../../styles/variables.css';

.container {
  margin-bottom: var(--space-md);
}

.label {
  display: block;
  margin-bottom: var(--space-xs);
  font-weight: 500;
  color: var(--text-medium);
  font-size: var(--font-size-sm);
}

.requiredMark {
  color: var(--error-color);
  margin-left: var(--space-xs);
}

.inputWrapper {
  position: relative;
  border: 1px solid #ddd;
  border-radius: var(--radius-md);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
  background-color: var(--bg-white);
}

.focused {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(87, 172, 90, 0.2);
}

.disabled {
  background-color: var(--bg-light);
  opacity: 0.7;
  cursor: not-allowed;
}

.hasError .inputWrapper {
  border-color: var(--error-color);
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.1);
}

.input {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-md);
  background: transparent;
  color: var(--text-dark);
}

.input:focus {
  outline: none;
}

.input::placeholder {
  color: var(--text-light);
}

.input:disabled {
  cursor: not-allowed;
}

.errorMessage {
  color: var(--error-color);
  font-size: var(--font-size-xs);
  margin-top: var(--space-xs);
}

.helperText {
  color: var(--text-light);
  font-size: var(--font-size-xs);
  margin-top: var(--space-xs);
}

/* 特定輸入類型樣式 */
.input[type="date"] {
  padding-right: var(--space-xs);
}

.input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield; /* 標準屬性，確保兼容性 */
}

.input[type="number"]::-webkit-outer-spin-button,
.input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .input {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--font-size-sm);
  }
}

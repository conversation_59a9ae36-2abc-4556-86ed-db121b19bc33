/* 後台全局變數定義 */
:root {
  /* 間距系統 */
  --admin-space-xxs: 0.25rem;
  --admin-space-xs: 0.5rem;
  --admin-space-sm: 0.75rem;
  --admin-space-md: 1rem;
  --admin-space-lg: 1.5rem;
  --admin-space-xl: 2rem;
  --admin-space-xxl: 3rem;

  /* 字體大小 */
  --admin-font-xs: 0.75rem;
  --admin-font-sm: 0.875rem;
  --admin-font-base: 1rem;
  --admin-font-lg: 1.125rem;
  --admin-font-xl: 1.25rem;
  --admin-font-xxl: 1.5rem;

  /* 字體粗細 */
  --admin-font-normal: 400;
  --admin-font-medium: 500;
  --admin-font-semibold: 600;
  --admin-font-bold: 700;

  /* 圓角 */
  --admin-radius-sm: 4px;
  --admin-radius-md: 8px;
  --admin-radius-lg: 12px;
  --admin-radius-xl: 16px;

  /* 主題顏色 */
  --admin-primary: #57AC5A;
  --admin-primary-light: rgba(87, 172, 90, 0.1);
  --admin-primary-dark: #458947;

  /* 文字顏色 */
  --admin-text-dark: #333333;
  --admin-text-medium: #666666;
  --admin-text-light: #999999;

  /* 背景顏色 */
  --admin-bg-white: #FFFFFF;
  --admin-bg-light: #F5F5F5;
  --admin-bg-lighter: #FAFAFA;

  /* 狀態顏色 */
  --admin-status-draft-bg: #F0F0F0;
  --admin-status-draft-text: #666666;
  --admin-status-published-bg: #E6F4E6;
  --admin-status-published-text: #57AC5A;
  --admin-status-archived-bg: #FFF3E6;
  --admin-status-archived-text: #FF9500;

  /* 陰影 */
  --admin-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --admin-shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
  --admin-shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.1);
  --admin-shadow-xl: 0 8px 12px rgba(0, 0, 0, 0.1);

  /* 動畫時間 */
  --admin-transition-fast: 0.15s;
  --admin-transition-normal: 0.3s;
  --admin-transition-slow: 0.5s;
}

/* 後台全局重置樣式 */
.adminRoot {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: var(--admin-text-dark);
  background-color: var(--admin-bg-light);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 通用容器樣式 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--admin-space-md);
}

/* 響應式斷點 */
@media (max-width: 1200px) {
  .container {
    max-width: 960px;
  }
}

@media (max-width: 992px) {
  .container {
    max-width: 720px;
  }
}

@media (max-width: 768px) {
  .container {
    max-width: 540px;
  }
}

@media (max-width: 576px) {
  .container {
    padding: 0 var(--admin-space-sm);
  }
} 
import React, { useState } from 'react';
import './ProjectCard.css';

export interface ProjectHighlight {
  icon: string;
  text: string;
}

export interface ProjectCardProps {
  id: string;
  title: string;
  description: string;
  price: number;
  status: 'active' | 'ordering_ended' | 'arrived' | 'completed';
  highlights: ProjectHighlight[];
  images: string[];
  onClick?: () => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  id,
  title,
  description,
  price,
  status,
  highlights,
  images,
  onClick,
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const handleDotClick = (index: number) => {
    setCurrentImageIndex(index);
  };

  return (
    <div className="project-card" onClick={onClick}>
      <div className={`project-status status-${status}`}>
        {status === 'active' && '進行中'}
        {status === 'ordering_ended' && '結束預購'}
        {status === 'arrived' && '已到貨'}
        {status === 'completed' && '已完成'}
      </div>
      <div className="card-content">
        <div className="card-header">
          <h2 className="card-title">{title}</h2>
          <div className="project-highlights">
            {highlights.map((h, idx) => (
              <div className="highlight-item" key={idx}>
                <div className="highlight-icon">{h.icon}</div>
                <span className="highlight-text">{h.text}</span>
              </div>
            ))}
          </div>
        </div>
        <p className="card-description">{description}</p>
        <div className="card-image-gallery-container">
          <div className="card-image-gallery">
            {images.map((src, idx) => (
              <img
                key={idx}
                src={src}
                alt={`${title} 圖片${idx + 1}`}
                className={`gallery-image ${idx === currentImageIndex ? 'active' : 'hidden'}`}
                loading="lazy"
              />
            ))}
          </div>
          <div className="gallery-dots">
            {images.map((_, idx) => (
              <span
                key={idx}
                className={`gallery-dot ${idx === currentImageIndex ? 'active' : ''}`}
                onClick={e => {
                  e.stopPropagation();
                  handleDotClick(idx);
                }}
              ></span>
            ))}
          </div>
        </div>
        <div className="card-footer">
          <div className="project-price">商品 NT$ {price.toLocaleString()} 起</div>
          <a
            href={`/preorder?id=${id}`}
            className="btn btn-primary"
            onClick={e => e.stopPropagation()}
          >
            查看詳情
          </a>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;
